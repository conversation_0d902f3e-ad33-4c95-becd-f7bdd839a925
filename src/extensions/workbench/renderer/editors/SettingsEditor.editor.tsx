import React, { useState, useEffect, useMemo, useCallback } from 'react';
import type { SettingDeclaration, AppSettings } from '@shared/types/settings';
// Import types and service
import { IpcErrorData, SettingsDeclarationsAndValuesResult, SettingsSetValueArgs } from '@shared/types/ipc';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import { themeService, AvailableTheme } from '@renderer/core/services/theme.service';
import './SettingsEditor.css';

// --- Компоненты для элементов управления ---

interface SettingControlProps {
  decl: SettingDeclaration; // Use SettingDeclaration from settings.ts
  currentValue: any;
  isModified: boolean;
  onChange: (key: string, value: any) => void;
}

const SettingCheckbox: React.FC<SettingControlProps> = ({ decl, currentValue, onChange }) => {
  const isChecked = currentValue === undefined ? !!decl.default : !!currentValue;
  return <input type="checkbox" checked={isChecked} onChange={(e) => onChange(decl.id, e.target.checked)} />;
};

const SettingNumberInput: React.FC<SettingControlProps> = ({ decl, currentValue, isModified, onChange }) => (
  <input type="number" value={currentValue as number} min={decl.minimum} max={decl.maximum} onChange={(e) => onChange(decl.id, e.target.valueAsNumber)} className={isModified ? 'modified' : ''} />
);

const SettingTextInput: React.FC<SettingControlProps> = ({ decl, currentValue, isModified, onChange }) => (
  <input type="text" value={currentValue as string} onChange={(e) => onChange(decl.id, e.target.value)} className={isModified ? 'modified' : ''} />
);

const SettingSelect: React.FC<SettingControlProps> = ({ decl, currentValue, isModified, onChange }) => (
  <select value={currentValue as string} onChange={(e) => onChange(decl.id, e.target.value)} className={isModified ? 'modified' : ''}>
    {(decl.enum ?? []).map((enumValue, index) => (
      <option key={enumValue} value={enumValue}>
        {decl.enumDescriptions?.[index] ?? enumValue}
      </option>
    ))}
  </select>
);

const SettingThemeSelect: React.FC<SettingControlProps> = ({ decl, currentValue, isModified, onChange }) => {
  const [availableThemes, setAvailableThemes] = useState<AvailableTheme[]>([]);

  useEffect(() => {
    setAvailableThemes(themeService.getAvailableThemes());
  }, []);

  return (
    <select value={currentValue as string} onChange={(e) => onChange(decl.id, e.target.value)} className={isModified ? 'modified' : ''}>
      {availableThemes.map((theme) => (
        <option key={theme.id} value={theme.id}>
          {theme.label}
        </option>
      ))}
    </select>
  );
};

// --- Основной компонент SettingsEditor ---

interface SettingsData {
  declarations: SettingDeclaration[]; // Use SettingDeclaration from settings.ts
  values: AppSettings;
}

const getCategoryFromId = (id: string): string => {
  const parts = id.split('.');
  const category = parts.length > 1 ? parts[0] : 'Other';
  return category.charAt(0).toUpperCase() + category.slice(1);
};

const getLabelFromId = (id: string, label?: string): string => {
    if (label) return label;
    const parts = id.split('.');
    return parts.slice(1).map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ');
};

export const SettingsEditor: React.FC = () => {
  const [settingsData, setSettingsData] = useState<SettingsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    const loadSettings = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Use new service and channel name
        const result = await ipcRendererService.invoke<SettingsDeclarationsAndValuesResult>('core:settings.getDeclarationsAndValues');
        if (isMounted) {
          // ipcRendererService throws on error, no need for isIpcError
          // Ensure declarations is an array and values is an object
          if (result && Array.isArray(result.declarations) && typeof result.values === 'object') {
            // Explicitly type declarations before sorting
            const declarations: SettingDeclaration[] = result.declarations;
            declarations.sort((a, b) => a.id.localeCompare(b.id));
            setSettingsData({ declarations, values: result.values }); // Use the typed declarations
            const firstCategory = declarations.length > 0 ? getCategoryFromId(declarations[0].id) : null;
            setSelectedCategory(firstCategory);
          } else {
            setError('Received invalid data format for settings.');
          }
        }
      } catch (err) {
        if (isMounted) {
          const errorData = err as IpcErrorData;
          setError(`Failed to load settings: ${errorData.message || String(err)}`);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    loadSettings();
    return () => { isMounted = false; };
  }, []);

  const handleSettingChange = useCallback(async (key: string, value: any) => {
    console.log(`[SettingsEditor] Setting ${key} to`, value);
    try {
      // Use new service, channel name, and args structure
      const args: SettingsSetValueArgs = { key: key as any, value };
      await ipcRendererService.invoke('core:settings.set', args);
      // ipcRendererService throws on error
      console.log(`[SettingsEditor] Setting ${key} updated successfully.`);
      // Update local state optimistically ONLY if successful
      setSettingsData(prevData => {
        if (!prevData) return null;
        const declaration = prevData.declarations.find(d => d.id === key);
        const newValues = { ...prevData.values };
        // Check if the new value is the default value
        if (declaration && value === declaration.default) {
          // If it's the default, remove the key from the saved values
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete newValues[key as keyof AppSettings];
        } else {
          // Otherwise, update or add the key
          // Apply fix here: Use 'as any' to bypass strict typing for optimistic update
          (newValues as any)[key] = value;
        }
        return { ...prevData, values: newValues };
      });
    } catch (err) {
      // Error is already logged by ipcRendererService
      const errorData = err as IpcErrorData;
      console.error(`[SettingsEditor] Failed to save setting ${key}:`, errorData.message, err);
      // Optionally show notification and rollback optimistic update if needed
      alert(`Failed to save setting "${key}": ${errorData.message || 'Unknown error'}`);
      // Consider reloading settings or rolling back the specific change in UI state
      // For now, just log the error and maybe show an alert
    }
    // Keep settingsData dependency if optimistic update needs rollback on error
  }, [settingsData]);

  const { categories, displayedSettings } = useMemo(() => {
    if (!settingsData) return { categories: [], displayedSettings: [] };
    const uniqueCategories = [...new Set(settingsData.declarations.map(decl => getCategoryFromId(decl.id)))];
    uniqueCategories.sort();
    const lowerSearchTerm = searchTerm.toLowerCase().trim();
    let filtered: SettingDeclaration[] = settingsData.declarations; // Use the correct type

    if (lowerSearchTerm) {
      filtered = settingsData.declarations.filter(decl =>
        decl.id.toLowerCase().includes(lowerSearchTerm) ||
        getLabelFromId(decl.id, decl.label).toLowerCase().includes(lowerSearchTerm) ||
        (decl.description && decl.description.toLowerCase().includes(lowerSearchTerm)) || // Check if description exists
        (decl.markdownDescription && decl.markdownDescription.toLowerCase().includes(lowerSearchTerm))
      );
    } else if (selectedCategory) {
      filtered = settingsData.declarations.filter(decl => getCategoryFromId(decl.id) === selectedCategory);
    } else if (uniqueCategories.length > 0) {
       const firstCategory = uniqueCategories[0];
       filtered = settingsData.declarations.filter(decl => getCategoryFromId(decl.id) === firstCategory);
    } else {
        filtered = [];
    }
    return { categories: uniqueCategories, displayedSettings: filtered };
  }, [settingsData, searchTerm, selectedCategory]);

   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
       const newSearchTerm = e.target.value;
       setSearchTerm(newSearchTerm);
       if (newSearchTerm.trim()) {
           setSelectedCategory(null); // Clear category selection when searching
       } else if (!selectedCategory && categories.length > 0) {
           // If search is cleared and no category selected, select the first one
           setSelectedCategory(categories[0]);
       }
   };

  // Функция для рендеринга элемента управления на основе декларации
  const renderSettingControl = (decl: SettingDeclaration) => {
    // Use optional chaining for safety
    const currentValue = settingsData?.values?.[decl.id as keyof AppSettings] ?? decl.default;
    const isModified = settingsData?.values?.hasOwnProperty(decl.id) ?? false;

    // Определяем тип контрола
    let controlType = decl.controlType;
    if (!controlType) {
        // Выводим тип контрола из типа данных
        switch (decl.type) {
            case 'boolean': controlType = 'checkbox'; break;
            case 'number': controlType = 'number'; break;
            case 'string': controlType = decl.enum ? 'select' : 'text'; break;
            // Добавляем обработку array и object по умолчанию как text (или можно добавить JSON редактор)
            case 'array':
            case 'object':
                 controlType = 'text'; // Placeholder - можно заменить на JSON editor
                 break;
            default: controlType = 'text';
        }
    }

    // Special case for theme selection
    if (decl.id === 'workbench.theme') {
        controlType = 'themeSelect';
    }

    const props: SettingControlProps = { decl, currentValue, isModified, onChange: handleSettingChange };

    // Рендерим соответствующий компонент
    switch (controlType) {
      case 'checkbox': return <SettingCheckbox {...props} />;
      case 'number': return <SettingNumberInput {...props} />;
      case 'select': return <SettingSelect {...props} />;
      case 'themeSelect': return <SettingThemeSelect {...props} />;
      case 'text':
      default:
        // Для array/object можно временно отображать JSON строку
        if (decl.type === 'array' || decl.type === 'object') {
            const stringValue = typeof currentValue === 'object' ? JSON.stringify(currentValue) : String(currentValue);
            // TODO: Реализовать реальное редактирование JSON
            return <input type="text" value={stringValue} readOnly className={isModified ? 'modified' : ''} title="JSON editing not yet supported"/>;
        }
        return <SettingTextInput {...props} />;
    }
  };

  if (isLoading) return <div className="settings-editor loading">Loading Settings...</div>;
  if (error) return <div className="settings-editor error">Error: {error}</div>;
  if (!settingsData) return <div className="settings-editor empty">No settings available.</div>;

  return (
    <div className="settings-editor layout">
      <div className="settings-sidebar">
        <input
          type="text"
          placeholder="Search settings"
          value={searchTerm}
          onChange={handleSearchChange}
          className="settings-search-input"
        />
        <ul className="settings-categories">
          {categories.map(category => (
            <li
              key={category}
              className={category === selectedCategory && !searchTerm ? 'active' : ''}
              onClick={() => {
                  setSelectedCategory(category);
                  setSearchTerm(''); // Clear search when category is clicked
              }}
            >
              {category}
            </li>
          ))}
        </ul>
      </div>
      <div className="settings-content">
        <h1>{searchTerm ? 'Search Results' : selectedCategory ?? 'Settings'}</h1>
        <div className="settings-list">
          {displayedSettings.length > 0 ? (
            displayedSettings.map(decl => (
              <div key={decl.id} className="setting-item">
                <div className="setting-details">
                  <label htmlFor={decl.id}>{getLabelFromId(decl.id, decl.label)}</label>
                  {/* Use description, fallback to markdownDescription if needed */}
                  <p>{decl.description || decl.markdownDescription}</p>
                </div>
                <div className="setting-control">
                  {renderSettingControl(decl)}
                </div>
              </div>
            ))
          ) : (
            <p>No settings found{searchTerm ? ' matching your search' : (selectedCategory ? ` in category "${selectedCategory}"` : '')}.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsEditor;
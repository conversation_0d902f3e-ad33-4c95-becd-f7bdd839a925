import React, { createContext, useContext, useMemo } from 'react'; // Added useMemo import
import { ipcRendererService } from '../services/ipcRendererService'; // Import core service
import type {
    IpcErrorData,
    CommandsExecuteArgs,
    ContextGetValueArgs,
    ContextGetValueResult,
    ContextChangedEventData,
    MenuShowContextMenuArgs
} from '@shared/types/ipc';
import { createContextKey } from '@shared/types/ipc';

// --- API Interface for Renderer Extensions ---
export interface ExtensionRendererAPI {
  /** Extension ID */
  readonly extensionId: string;

  /**
   * Executes a command registered in the main process.
   * Automatically uses the 'core:commands.execute' channel.
   * @param commandId The ID of the command to execute.
   * @param args Optional arguments for the command handler.
   * @returns A promise resolving with the command's result or rejecting with an IpcErrorData.
   */
  executeCommand<TResult = unknown>(commandId: string, ...args: any[]): Promise<TResult>;

  /**
   * Invokes an IPC handler registered by this extension's main process part.
   * Automatically prefixes the channel name with the extension ID.
   * @param channel The channel name within the extension (e.g., 'getBooks').
   * @param payload Optional payload for the handler.
   * @returns A promise resolving with the handler's result or rejecting with an IpcErrorData.
   */
  invoke<TResult = unknown, TArgs = unknown>(channel: string, payload?: TArgs): Promise<TResult>;

  /**
   * Gets a value from the main process context service.
   * @param key The context key.
   * @returns A promise resolving with the context value.
   */
  getContextValue<T = unknown>(key: string): Promise<T | undefined>;

  /**
   * Subscribes to changes of a specific context key from the main process.
   * @param key The context key to listen for.
   * @param listener The callback function to execute when the context value changes.
   * @returns A function to unsubscribe the listener.
   */
  onContextChange<T = any>(key: string, listener: (value: T) => void): () => void;

  /**
   * Shows a context menu defined in the main process.
   * @param contextIdentifier The identifier for the context menu contribution point.
   * @param menuArgs Optional arguments for building the menu.
   */
  showContextMenu(contextIdentifier: string, menuArgs?: any): Promise<void>;

  /**
   * Shows an information notification.
   * (Assumes a 'core:notifications.show' handler exists or will be added)
   */
  showInformationMessage(message: string, options?: any): Promise<void>;
   /**
   * Shows a warning notification.
   */
  showWarningMessage(message: string, options?: any): Promise<void>;
   /**
   * Shows an error notification.
   */
  showErrorMessage(message: string, options?: any): Promise<void>;

  // Add other necessary APIs here...
}

// --- Implementation ---
class ExtensionRendererAPIImpl implements ExtensionRendererAPI {
  constructor(public readonly extensionId: string) {}

  async executeCommand<TResult = unknown>(commandId: string, ...args: any[]): Promise<TResult> {
    const commandArgs: CommandsExecuteArgs = { commandId, args: args as any };
    // Explicitly type the invoke call for clarity
    return ipcRendererService.invoke<TResult, CommandsExecuteArgs>('core:commands.execute', commandArgs);
  }

  async invoke<TResult = unknown, TArgs = unknown>(channel: string, payload?: TArgs): Promise<TResult> {
    const prefixedChannel = `${this.extensionId}:${channel}`;
    // Explicitly type the invoke call
    return ipcRendererService.invoke<TResult, TArgs>(prefixedChannel, payload);
  }

  async getContextValue<T = unknown>(key: string): Promise<T | undefined> {
      const args: ContextGetValueArgs = { key: createContextKey(key) };
      // Result might be undefined if key doesn't exist
      const result = await ipcRendererService.invoke<ContextGetValueResult>('core:context.get', args);
      return result as T | undefined;
  }

  onContextChange<T = any>(key: string, listener: (value: T) => void): () => void {
      const wrappedListener = (payload: ContextChangedEventData) => {
          if (payload && payload.key === key) {
              listener(payload.value as T);
          }
      };
      // Subscribe to the generic core event
      return ipcRendererService.on<ContextChangedEventData>('core:context.changed', wrappedListener);
  }

  async showContextMenu(contextIdentifier: string, menuArgs?: any): Promise<void> {
      const args: MenuShowContextMenuArgs = { contextIdentifier, menuArgs };
      // Assuming 'core:menu.showContextMenu' exists and returns void on success
      await ipcRendererService.invoke<void, MenuShowContextMenuArgs>('core:menu.showContextMenu', args);
  }

  // Helper for showing notifications
  private async showNotification(type: string, message: string, options?: any): Promise<void> {
      const notificationOptions = {
          ...options,
          message,
          type,
      };
      // TODO: Ensure 'core:notifications.show' handler exists in main/ipcHandlers.ts
      // This might need adjustment based on how notifications are implemented.
      // It could be an event send from main, or an invoke like this.
      try {
          // Assuming an invoke channel for simplicity, adjust if it's an event
          await ipcRendererService.invoke<void, any>('core:notifications.show', notificationOptions);
      } catch (err: unknown) { // Use unknown
           const errorData = err as IpcErrorData;
           console.error(`[Extension ${this.extensionId}] Failed to show ${type} notification:`, errorData?.message, err); // Optional chaining
           // Fallback to alert?
           // alert(`${type.toUpperCase()}: ${message}`);
      }
  }

  showInformationMessage(message: string, options?: any): Promise<void> {
      return this.showNotification('info', message, options);
  }
  showWarningMessage(message: string, options?: any): Promise<void> {
       return this.showNotification('warning', message, options);
  }
  showErrorMessage(message: string, options?: any): Promise<void> {
       return this.showNotification('error', message, options);
  }

}

// --- Context Creation ---
// Create a context with a default value (or null)
const ExtensionRendererContext = createContext<ExtensionRendererAPI | null>(null);

// --- Hook for easy consumption ---
export function useExtensionRendererContext(): ExtensionRendererAPI {
  const context = useContext(ExtensionRendererContext);
  if (!context) {
    throw new Error('useExtensionRendererContext must be used within an ExtensionRendererContext.Provider');
  }
  return context;
}

// --- Provider Component ---
// This will be used by core components (Sidebar, Panel, EditorGroup) to wrap extension components
interface ExtensionRendererProviderProps {
  extensionId: string;
  children: React.ReactNode;
}

export const ExtensionRendererProvider: React.FC<ExtensionRendererProviderProps> = ({ extensionId, children }) => {
  // Create a memoized API instance for the given extension ID
  const apiInstance = useMemo(() => new ExtensionRendererAPIImpl(extensionId), [extensionId]);

  return (
    <ExtensionRendererContext.Provider value={apiInstance}>
      {children}
    </ExtensionRendererContext.Provider>
  );
};

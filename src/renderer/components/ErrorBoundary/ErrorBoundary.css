.error-boundary-fallback {
  padding: 20px;
  margin: 10px;
  border: 1px solid #ff6b6b;
  border-radius: 4px;
  background-color: #fff5f5;
  color: #c92a2a;
  font-family: system-ui, -apple-system, sans-serif;
}

.error-boundary-fallback h3 {
  margin: 0 0 10px 0;
  color: #c92a2a;
  font-size: 16px;
  font-weight: 600;
}

.error-boundary-fallback p {
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.4;
}

.error-boundary-fallback details {
  margin-bottom: 15px;
}

.error-boundary-fallback summary {
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 5px 0;
  user-select: none;
}

.error-boundary-fallback summary:hover {
  color: #a61e1e;
}

.error-boundary-fallback .error-details {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  font-size: 12px;
  font-family: '<PERSON>', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  white-space: pre-wrap;
  overflow: auto;
  max-height: 200px;
  color: #495057;
}

.error-boundary-fallback .error-id {
  font-size: 12px;
  color: #6c757d;
  margin: 0 0 15px 0;
  font-family: monospace;
}

.error-boundary-fallback .error-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.error-boundary-fallback button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.error-boundary-fallback .retry-button {
  background-color: #007bff;
  color: white;
}

.error-boundary-fallback .retry-button:hover {
  background-color: #0056b3;
}

.error-boundary-fallback .reload-button {
  background-color: #6c757d;
  color: white;
}

.error-boundary-fallback .reload-button:hover {
  background-color: #545b62;
}

/* Compact error boundary for smaller components */
.error-boundary-fallback.compact {
  padding: 10px;
  margin: 5px;
  font-size: 12px;
}

.error-boundary-fallback.compact h3 {
  font-size: 14px;
  margin-bottom: 5px;
}

.error-boundary-fallback.compact p {
  margin-bottom: 10px;
  font-size: 12px;
}

.error-boundary-fallback.compact button {
  padding: 6px 12px;
  font-size: 12px;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .error-boundary-fallback {
    background-color: #2d1b1b;
    border-color: #ff8787;
    color: #ff8787;
  }
  
  .error-boundary-fallback h3 {
    color: #ff8787;
  }
  
  .error-boundary-fallback .error-details {
    background-color: #1a1a1a;
    border-color: #404040;
    color: #e9ecef;
  }
  
  .error-boundary-fallback .error-id {
    color: #adb5bd;
  }
  
  .error-boundary-fallback .retry-button {
    background-color: #0d6efd;
  }
  
  .error-boundary-fallback .retry-button:hover {
    background-color: #0b5ed7;
  }
  
  .error-boundary-fallback .reload-button {
    background-color: #495057;
  }
  
  .error-boundary-fallback .reload-button:hover {
    background-color: #343a40;
  }
}

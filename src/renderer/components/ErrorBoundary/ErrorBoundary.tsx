import React, { Component, ReactNode } from 'react';
import { ErrorInfo, ErrorSeverity } from '@shared/types/error-handling';
import { ipcRendererService } from '@renderer/core/services/ipcRendererService';
import { IpcChannels } from '@shared/constants/ipc-channels';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  componentName?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  errorId?: string;
}

/**
 * React Error Boundary component that catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { onError, componentName } = this.props;
    
    // Log error to console for development
    console.error(`[ErrorBoundary${componentName ? ` - ${componentName}` : ''}] Caught error:`, error, errorInfo);
    
    // Generate unique error ID
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Update state with error details
    this.setState({ errorInfo, errorId });
    
    // Report error to main process for logging
    this.reportErrorToMain(error, errorInfo, errorId);
    
    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }
  }

  private async reportErrorToMain(error: Error, errorInfo: React.ErrorInfo, errorId: string) {
    try {
      const errorData: Partial<ErrorInfo> = {
        id: errorId,
        message: error.message,
        stack: error.stack,
        timestamp: Date.now(),
        source: `renderer-${this.props.componentName || 'unknown'}`,
        severity: ErrorSeverity.HIGH,
        metadata: {
          componentStack: errorInfo.componentStack,
          errorBoundary: this.props.componentName || 'ErrorBoundary',
          userAgent: navigator.userAgent,
        },
        handled: true,
      };

      await ipcRendererService.invoke(IpcChannels.ERROR_REPORT, errorData);
    } catch (reportError) {
      console.error('[ErrorBoundary] Failed to report error to main process:', reportError);
    }
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined, errorId: undefined });
  };

  private handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { fallback, componentName } = this.props;
      const { error, errorId } = this.state;

      // Use custom fallback if provided
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <div className="error-boundary-fallback" style={{
          padding: '20px',
          margin: '10px',
          border: '1px solid #ff6b6b',
          borderRadius: '4px',
          backgroundColor: '#fff5f5',
          color: '#c92a2a',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#c92a2a' }}>
            Something went wrong{componentName ? ` in ${componentName}` : ''}
          </h3>
          
          <p style={{ margin: '0 0 15px 0', fontSize: '14px' }}>
            An unexpected error occurred. The error has been reported and logged.
          </p>
          
          {error && (
            <details style={{ marginBottom: '15px' }}>
              <summary style={{ cursor: 'pointer', fontSize: '14px', fontWeight: 'bold' }}>
                Error Details
              </summary>
              <div style={{ 
                marginTop: '10px', 
                padding: '10px', 
                backgroundColor: '#f8f9fa', 
                border: '1px solid #dee2e6',
                borderRadius: '3px',
                fontSize: '12px',
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                <strong>Error:</strong> {error.message}
                {error.stack && (
                  <>
                    <br /><br />
                    <strong>Stack Trace:</strong>
                    <br />
                    {error.stack}
                  </>
                )}
              </div>
            </details>
          )}
          
          {errorId && (
            <p style={{ fontSize: '12px', color: '#6c757d', margin: '0 0 15px 0' }}>
              Error ID: {errorId}
            </p>
          )}
          
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={this.handleRetry}
              style={{
                padding: '8px 16px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              Try Again
            </button>
            
            <button
              onClick={this.handleReload}
              style={{
                padding: '8px 16px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              Reload Application
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component that wraps a component with an ErrorBoundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook for functional components to handle errors
 */
export function useErrorHandler() {
  const handleError = React.useCallback((error: Error, errorInfo?: unknown) => {
    // Create a synthetic error boundary-like behavior for functional components
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    console.error('[useErrorHandler] Caught error:', error, errorInfo);
    
    // Report to main process
    const reportError = async () => {
      try {
        const errorData: Partial<ErrorInfo> = {
          id: errorId,
          message: error.message,
          stack: error.stack,
          timestamp: Date.now(),
          source: 'renderer-hook',
          severity: ErrorSeverity.MEDIUM,
          metadata: {
            errorInfo,
            userAgent: navigator.userAgent,
          },
          handled: true,
        };

        await ipcRendererService.invoke(IpcChannels.ERROR_REPORT, errorData);
      } catch (reportError) {
        console.error('[useErrorHandler] Failed to report error to main process:', reportError);
      }
    };
    
    reportError();
  }, []);

  return handleError;
}

.health-monitor {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--background-color, #2d2d2d);
  border: 1px solid var(--border-color, #444);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  font-family: var(--font-family, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
  font-size: 12px;
  color: var(--text-color, #ffffff);
  max-width: 400px;
  min-width: 200px;
}

.health-monitor.loading,
.health-monitor.error {
  background: var(--background-color-secondary, #3d3d3d);
}

.health-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 8px;
  user-select: none;
  transition: background-color 0.2s ease;
}

.health-indicator:hover {
  background: var(--hover-color, rgba(255, 255, 255, 0.1));
}

.status-icon {
  font-size: 14px;
  font-weight: bold;
  min-width: 16px;
  text-align: center;
}

.status-text {
  flex: 1;
  font-weight: 500;
}

.expand-icon {
  font-size: 10px;
  color: var(--text-color-secondary, #aaa);
  transition: transform 0.2s ease;
}

.health-details {
  border-top: 1px solid var(--border-color, #444);
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.health-statistics {
  margin-bottom: 16px;
}

.health-statistics h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color-primary, #fff);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: var(--background-color-tertiary, rgba(255, 255, 255, 0.05));
  border-radius: 4px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-color-secondary, #aaa);
}

.stat-value {
  font-weight: 600;
  font-size: 12px;
}

.service-health-list h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color-primary, #fff);
}

.no-services {
  text-align: center;
  color: var(--text-color-secondary, #aaa);
  font-style: italic;
  padding: 16px;
}

.service-health-item {
  margin-bottom: 8px;
  border: 1px solid var(--border-color-light, #555);
  border-radius: 4px;
  overflow: hidden;
}

.service-header {
  display: flex;
  align-items: center;
  padding: 8px;
  gap: 8px;
  background: var(--background-color-tertiary, rgba(255, 255, 255, 0.03));
}

.service-status {
  font-size: 12px;
  font-weight: bold;
  min-width: 16px;
  text-align: center;
}

.service-name {
  flex: 1;
  font-weight: 500;
  font-size: 12px;
}

.service-response-time {
  font-size: 11px;
  color: var(--text-color-secondary, #aaa);
  min-width: 40px;
  text-align: right;
}

.check-button {
  background: none;
  border: none;
  color: var(--text-color-secondary, #aaa);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  transition: background-color 0.2s ease;
}

.check-button:hover {
  background: var(--hover-color, rgba(255, 255, 255, 0.1));
  color: var(--text-color-primary, #fff);
}

.service-details {
  padding: 8px;
  border-top: 1px solid var(--border-color-light, #555);
  background: var(--background-color-quaternary, rgba(255, 255, 255, 0.02));
}

.service-error {
  color: #f44336;
  font-size: 11px;
  margin-bottom: 4px;
  word-break: break-word;
}

.service-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 10px;
  color: var(--text-color-secondary, #aaa);
}

.service-stats span {
  background: var(--background-color-tertiary, rgba(255, 255, 255, 0.05));
  padding: 2px 6px;
  border-radius: 2px;
}

.health-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.health-actions button {
  background: var(--button-background, #0078d4);
  color: var(--button-text, #fff);
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.health-actions button:hover {
  background: var(--button-background-hover, #106ebe);
}

.health-actions button:last-child {
  background: var(--button-background-secondary, #666);
}

.health-actions button:last-child:hover {
  background: var(--button-background-secondary-hover, #777);
}

.error-message {
  color: #f44336;
  font-size: 11px;
  margin-bottom: 8px;
  padding: 8px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
  border-left: 3px solid #f44336;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .health-monitor {
    --background-color: #2d2d2d;
    --background-color-secondary: #3d3d3d;
    --background-color-tertiary: rgba(255, 255, 255, 0.05);
    --background-color-quaternary: rgba(255, 255, 255, 0.02);
    --border-color: #444;
    --border-color-light: #555;
    --text-color: #ffffff;
    --text-color-primary: #fff;
    --text-color-secondary: #aaa;
    --hover-color: rgba(255, 255, 255, 0.1);
    --button-background: #0078d4;
    --button-background-hover: #106ebe;
    --button-background-secondary: #666;
    --button-background-secondary-hover: #777;
    --button-text: #fff;
  }
}

/* Light theme adjustments */
@media (prefers-color-scheme: light) {
  .health-monitor {
    --background-color: #ffffff;
    --background-color-secondary: #f5f5f5;
    --background-color-tertiary: rgba(0, 0, 0, 0.05);
    --background-color-quaternary: rgba(0, 0, 0, 0.02);
    --border-color: #ddd;
    --border-color-light: #eee;
    --text-color: #333333;
    --text-color-primary: #000;
    --text-color-secondary: #666;
    --hover-color: rgba(0, 0, 0, 0.05);
    --button-background: #0078d4;
    --button-background-hover: #106ebe;
    --button-background-secondary: #999;
    --button-background-secondary-hover: #888;
    --button-text: #fff;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .health-monitor {
    bottom: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .service-stats {
    flex-direction: column;
    gap: 4px;
  }
}

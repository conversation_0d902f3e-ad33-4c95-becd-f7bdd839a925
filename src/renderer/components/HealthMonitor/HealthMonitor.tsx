/**
 * Health monitoring component for displaying system health status
 */

import React, { useState, useEffect, useCallback } from 'react';
import { ipcRendererService } from '../../core/services/ipcRendererService';
import './HealthMonitor.css';

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical' | 'unknown';
  score: number;
  timestamp: number;
}

interface ServiceHealth {
  serviceName: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'critical' | 'unknown';
  lastCheck: number;
  lastHealthy: number;
  consecutiveFailures: number;
  totalChecks: number;
  totalFailures: number;
  averageResponseTime: number;
  lastError?: string;
}

interface HealthStatistics {
  totalServices: number;
  healthyServices: number;
  degradedServices: number;
  unhealthyServices: number;
  criticalServices: number;
  unknownServices: number;
  globalHealthScore: number;
  systemStatus: string;
  averageResponseTime: number;
  totalChecks: number;
  totalFailures: number;
  timestamp: number;
}

export const HealthMonitor: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [serviceHealth, setServiceHealth] = useState<ServiceHealth[]>([]);
  const [statistics, setStatistics] = useState<HealthStatistics | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial health data
  const loadHealthData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [systemResult, servicesResult, statsResult] = await Promise.all([
        ipcRendererService.invoke('health:getSystemHealth', {}),
        ipcRendererService.invoke('health:getAllServiceHealth', {}),
        ipcRendererService.invoke('health:getStatistics', {}),
      ]);

      if (systemResult.success) {
        setSystemHealth(systemResult.data);
      }

      if (servicesResult.success) {
        setServiceHealth(servicesResult.data);
      }

      if (statsResult.success) {
        setStatistics(statsResult.data);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load health data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Manual health check for a service
  const checkService = useCallback(async (serviceName: string) => {
    try {
      const result = await ipcRendererService.invoke('health:checkService', { serviceName });
      if (result.success) {
        // Refresh health data after manual check
        await loadHealthData();
      }
    } catch (err) {
      setError(`Failed to check ${serviceName}: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  }, [loadHealthData]);

  // Setup health monitoring listeners
  useEffect(() => {
    // Load initial data
    loadHealthData();

    // Listen for health updates
    const handleSystemHealthUpdate = (data: SystemHealth) => {
      setSystemHealth(data);
    };

    const handleUnhealthyServicesUpdate = (data: ServiceHealth[]) => {
      // Update only the unhealthy services in our state
      setServiceHealth(prevServices => {
        const updatedServices = [...prevServices];
        data.forEach(unhealthyService => {
          const index = updatedServices.findIndex(s => s.serviceName === unhealthyService.serviceName);
          if (index >= 0) {
            updatedServices[index] = unhealthyService;
          }
        });
        return updatedServices;
      });
    };

    // Register IPC listeners
    ipcRendererService.on('health:systemHealthUpdate', handleSystemHealthUpdate);
    ipcRendererService.on('health:unhealthyServicesUpdate', handleUnhealthyServicesUpdate);

    // Cleanup listeners on unmount
    return () => {
      ipcRendererService.removeAllListeners('health:systemHealthUpdate');
      ipcRendererService.removeAllListeners('health:unhealthyServicesUpdate');
    };
  }, [loadHealthData]);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'degraded': return '#FF9800';
      case 'unhealthy': return '#F44336';
      case 'critical': return '#D32F2F';
      default: return '#9E9E9E';
    }
  };

  const getStatusIcon = (status: string): string => {
    switch (status) {
      case 'healthy': return '✓';
      case 'degraded': return '⚠';
      case 'unhealthy': return '✗';
      case 'critical': return '🚨';
      default: return '?';
    }
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  if (isLoading) {
    return (
      <div className="health-monitor loading">
        <div className="health-indicator">
          <span className="status-icon">⏳</span>
          <span className="status-text">Loading health data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="health-monitor error">
        <div className="health-indicator" onClick={() => loadHealthData()}>
          <span className="status-icon">⚠</span>
          <span className="status-text">Health monitoring error</span>
        </div>
        {isExpanded && (
          <div className="health-details">
            <div className="error-message">{error}</div>
            <button onClick={() => loadHealthData()}>Retry</button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="health-monitor">
      <div 
        className="health-indicator" 
        onClick={() => setIsExpanded(!isExpanded)}
        style={{ cursor: 'pointer' }}
      >
        <span 
          className="status-icon"
          style={{ color: systemHealth ? getStatusColor(systemHealth.status) : '#9E9E9E' }}
        >
          {systemHealth ? getStatusIcon(systemHealth.status) : '?'}
        </span>
        <span className="status-text">
          {systemHealth ? `System ${systemHealth.status}` : 'Unknown'}
          {systemHealth && ` (${Math.round(systemHealth.score * 100)}%)`}
        </span>
        <span className="expand-icon">{isExpanded ? '▼' : '▶'}</span>
      </div>

      {isExpanded && (
        <div className="health-details">
          {statistics && (
            <div className="health-statistics">
              <h4>System Statistics</h4>
              <div className="stats-grid">
                <div className="stat-item">
                  <span className="stat-label">Services:</span>
                  <span className="stat-value">{statistics.totalServices}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Healthy:</span>
                  <span className="stat-value" style={{ color: getStatusColor('healthy') }}>
                    {statistics.healthyServices}
                  </span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Issues:</span>
                  <span className="stat-value" style={{ color: getStatusColor('unhealthy') }}>
                    {statistics.degradedServices + statistics.unhealthyServices + statistics.criticalServices}
                  </span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">Avg Response:</span>
                  <span className="stat-value">{formatDuration(statistics.averageResponseTime)}</span>
                </div>
              </div>
            </div>
          )}

          <div className="service-health-list">
            <h4>Service Health</h4>
            {serviceHealth.length === 0 ? (
              <div className="no-services">No service health data available</div>
            ) : (
              serviceHealth.map(service => (
                <div key={service.serviceName} className="service-health-item">
                  <div className="service-header">
                    <span 
                      className="service-status"
                      style={{ color: getStatusColor(service.status) }}
                    >
                      {getStatusIcon(service.status)}
                    </span>
                    <span className="service-name">{service.serviceName}</span>
                    <span className="service-response-time">
                      {formatDuration(service.averageResponseTime)}
                    </span>
                    <button 
                      className="check-button"
                      onClick={() => checkService(service.serviceName)}
                      title="Manual health check"
                    >
                      🔄
                    </button>
                  </div>
                  
                  {service.status !== 'healthy' && (
                    <div className="service-details">
                      {service.lastError && (
                        <div className="service-error">Error: {service.lastError}</div>
                      )}
                      <div className="service-stats">
                        <span>Failures: {service.consecutiveFailures}/{service.totalFailures}</span>
                        <span>Last check: {formatTimestamp(service.lastCheck)}</span>
                        <span>Last healthy: {formatTimestamp(service.lastHealthy)}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>

          <div className="health-actions">
            <button onClick={loadHealthData}>Refresh All</button>
            <button onClick={() => setIsExpanded(false)}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
};

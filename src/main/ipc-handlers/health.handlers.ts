/**
 * IPC handlers for health monitoring
 */

import { IpcServiceAPI } from '../services/ipc.service';
import { HealthService, ServiceHealth, HealthStatus } from '../services/health.service';
import { LoggingServiceAPI } from '../services/logging.service';
import { validateInput } from '@shared/validation/utils';
import { z } from 'zod';

/**
 * Health monitoring IPC handlers
 */
export class HealthIpcHandlers {
  private readonly logger: LoggingServiceAPI;

  constructor(
    private readonly ipcService: IpcServiceAPI,
    private readonly healthService: HealthService,
    logger: LoggingServiceAPI
  ) {
    this.logger = logger.createScopedLogger('HealthIpcHandlers');
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // Get overall system health
    this.ipcService.handle('health:getSystemHealth', async () => {
      return {
        status: this.healthService.getSystemHealthStatus(),
        score: this.healthService.getGlobalHealthScore(),
        timestamp: Date.now(),
      };
    });

    // Get health for all services
    this.ipcService.handle('health:getAllServiceHealth', async () => {
      const healthMap = this.healthService.getAllServiceHealth();
      const healthArray: ServiceHealth[] = Array.from(healthMap.values());
      return healthArray;
    });

    // Get health for specific service
    this.ipcService.handle('health:getServiceHealth', async (args) => {
      const { serviceName } = validateInput(
        args,
        z.object({
          serviceName: z.string().min(1),
        })
      );

      const health = this.healthService.getServiceHealth(serviceName);
      if (!health) {
        throw new Error(`Service '${serviceName}' not found in health monitoring`);
      }

      return health;
    });

    // Manually trigger health check for a service
    this.ipcService.handle('health:checkService', async (args) => {
      const { serviceName } = validateInput(
        args,
        z.object({
          serviceName: z.string().min(1),
        })
      );

      const result = await this.healthService.checkServiceHealth(serviceName);
      if (!result) {
        throw new Error(`Service '${serviceName}' not found in health monitoring`);
      }

      return result;
    });

    // Get health monitoring statistics
    this.ipcService.handle('health:getStatistics', async () => {
      const allHealth = this.healthService.getAllServiceHealth();
      const services = Array.from(allHealth.values());

      const statistics = {
        totalServices: services.length,
        healthyServices: services.filter(s => s.status === HealthStatus.HEALTHY).length,
        degradedServices: services.filter(s => s.status === HealthStatus.DEGRADED).length,
        unhealthyServices: services.filter(s => s.status === HealthStatus.UNHEALTHY).length,
        criticalServices: services.filter(s => s.status === HealthStatus.CRITICAL).length,
        unknownServices: services.filter(s => s.status === HealthStatus.UNKNOWN).length,
        globalHealthScore: this.healthService.getGlobalHealthScore(),
        systemStatus: this.healthService.getSystemHealthStatus(),
        averageResponseTime: services.length > 0 
          ? services.reduce((sum, s) => sum + s.averageResponseTime, 0) / services.length 
          : 0,
        totalChecks: services.reduce((sum, s) => sum + s.totalChecks, 0),
        totalFailures: services.reduce((sum, s) => sum + s.totalFailures, 0),
        timestamp: Date.now(),
      };

      return statistics;
    });

    // Get health history/trends (simplified version)
    this.ipcService.handle('health:getHealthTrends', async (args) => {
      const { serviceName, timeRange } = validateInput(
        args,
        z.object({
          serviceName: z.string().optional(),
          timeRange: z.number().min(1).max(86400000).default(3600000), // Default 1 hour, max 24 hours
        })
      );

      // For now, return current health status
      // In a full implementation, you'd store historical data
      if (serviceName) {
        const health = this.healthService.getServiceHealth(serviceName);
        return health ? [health] : [];
      } else {
        const allHealth = this.healthService.getAllServiceHealth();
        return Array.from(allHealth.values());
      }
    });

    this.logger.info('Health monitoring IPC handlers registered');
  }

  /**
   * Start broadcasting health updates to renderer
   */
  startHealthBroadcasting(intervalMs: number = 30000): void {
    setInterval(() => {
      try {
        const systemHealth = {
          status: this.healthService.getSystemHealthStatus(),
          score: this.healthService.getGlobalHealthScore(),
          timestamp: Date.now(),
        };

        // Broadcast system health update
        this.ipcService.sendToRenderer('health:systemHealthUpdate', systemHealth);

        // Broadcast detailed service health if there are issues
        const allHealth = this.healthService.getAllServiceHealth();
        const unhealthyServices = Array.from(allHealth.values()).filter(
          s => s.status !== HealthStatus.HEALTHY && s.status !== HealthStatus.UNKNOWN
        );

        if (unhealthyServices.length > 0) {
          this.ipcService.sendToRenderer('health:unhealthyServicesUpdate', unhealthyServices);
        }

      } catch (error) {
        this.logger.error('Error broadcasting health updates:', error);
      }
    }, intervalMs);

    this.logger.info(`Started health broadcasting every ${intervalMs}ms`);
  }

  /**
   * Dispose health handlers
   */
  dispose(): void {
    // In a full implementation, you'd unregister handlers here
    // The current IpcService doesn't support unregistering handlers
    this.logger.info('Health IPC handlers disposed');
  }
}

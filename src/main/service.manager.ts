import {<PERSON><PERSON><PERSON>Window} from 'electron';
import {AppLifecycle, AppLifecycleEvent} from '@shared/types/lifecycle-events';
import {LoggingServiceAPI} from '@services/logging.service';
import {WindowManager} from '@main/window.manager';
// import {SettingsService, SettingsServiceAPI} from '@services/settings.service'; // Removed
import {ConfigurationService, ConfigurationServiceAPI} from '@services/configuration.service'; // Added
import {StorageService, StorageServiceAPI} from '@services/storage.service';
import {CommandService, CommandServiceAPI} from '@services/command.service';
import {ViewService, ViewServiceAPI} from '@services/view.service';
import {IpcService, IpcServiceAPI} from '@services/ipc.service';
import {NotificationService, NotificationServiceAPI} from '@services/notification.service';
import {DialogService, DialogServiceAPI} from '@services/dialog.service';
import {ContextService, ContextServiceAPI} from '@services/context.service';
import {MenuService, MenuServiceAPI} from '@services/menu.service';
import {EditorService, EditorServiceAPI} from '@services/editor.service';
import {GitService, GitServiceAPI} from '@services/git.service';
import {KeybindingService, KeybindingServiceAPI} from '@services/keybinding.service';
import {AIService, AIServiceAPI} from '@services/ai.service';
import {LifecycleService} from '@services/lifecycle.service';
import {BaseService, IBaseService} from '@services/base.service';
import {ServiceRegistry} from '@main/service-registry';
import {HealthService} from './services/health.service';
import {
  FileSystemHealthChecker,
  GitHealthChecker,
  IpcHealthChecker,
  LoggingHealthChecker,
  MemoryHealthChecker,
  StorageHealthChecker
} from '@services/health-checkers';
import {HealthIpcHandlers} from './ipc-handlers/health.handlers';
import {ResourceMonitorService, ResourceMonitorServiceAPI} from './services/resource-monitor.service';

/**
 * Набор API основных сервисов, предоставляемых расширениям.
 * Стандартизованы на основе IBaseServiceAPI.
 */
export interface CoreServicesAPI {
  commands: CommandServiceAPI;
  views: ViewServiceAPI;
  storage: StorageServiceAPI;
  configuration: ConfigurationServiceAPI; // Changed from settings
  ipc: IpcServiceAPI;
  notifications: NotificationServiceAPI;
  logger: LoggingServiceAPI;
  dialogs: DialogServiceAPI;
  context: ContextServiceAPI;
  menus: MenuServiceAPI;
  editors: EditorServiceAPI;
  git: GitServiceAPI;
  ai: AIServiceAPI;
  lifecycle: AppLifecycle;
  health: HealthService;
  resourceMonitor: ResourceMonitorServiceAPI;
}

/**
 * Управляет жизненным циклом и доступом к основным сервисам приложения.
 * Создает экземпляры сервисов и управляет их зависимостями.
 */
export class ServiceManager extends BaseService {
  private readonly serviceRegistry: ServiceRegistry;
  private _windowManager: WindowManager | null = null; // Added private property for WindowManager
  
  // --- Private service instances ---
  // private readonly _settings: SettingsServiceAPI; // Removed
  private readonly _configuration: ConfigurationServiceAPI; // Added
  private readonly _storage: StorageServiceAPI;
  private readonly _commands: CommandServiceAPI;
  private readonly _context: ContextServiceAPI;
  private readonly _views: ViewServiceAPI;
  private readonly _ipc: IpcServiceAPI;
  private readonly _notifications: NotificationServiceAPI;
  private readonly _dialogs: DialogServiceAPI;
  private readonly _menus: MenuServiceAPI;
  private readonly _editors: EditorServiceAPI;
  private readonly _git: GitServiceAPI;
  private readonly _keybinding: KeybindingServiceAPI;
  private readonly _ai: AIServiceAPI;
  private readonly _lifecycle: LifecycleService;
  private readonly _health: HealthService;
  private readonly _resourceMonitor: ResourceMonitorServiceAPI;
  private _healthIpcHandlers: HealthIpcHandlers | null = null;
  
  constructor(logger: LoggingServiceAPI) { // Removed WindowManager from constructor
    super(logger, "ServiceManager");
    this.logger.info("Instantiating core services...");
    
    this.serviceRegistry = new ServiceRegistry(this.logger);
    
    this._lifecycle = new LifecycleService(logger);
    this.registerService('lifecycle', this._lifecycle);
    
    // Создаем основные сервисы в порядке зависимостей
    this._ipc = new IpcService(logger);
    this.registerService('ipc', this._ipc);
    
    // Instantiate ConfigurationService instead of SettingsService
    this._configuration = new ConfigurationService(logger, this._ipc);
    this.registerService('configuration', this._configuration, ['ipc']);
    
    // Removed SettingsService instantiation
    
    this._storage = new StorageService(logger, this._ipc);
    this.registerService('storage', this._storage, ['ipc']);
    
    this._context = new ContextService(logger, this._ipc);
    this.registerService('context', this._context, ['ipc']);
    
    this._keybinding = new KeybindingService(logger, this._context, this._ipc);
    this.registerService('keybinding', this._keybinding, ['context', 'ipc']);
    
    this._views = new ViewService(logger, this._ipc);
    this.registerService('views', this._views, ['ipc']);
    
    this._notifications = new NotificationService(logger, this._ipc);
    this.registerService('notifications', this._notifications, ['ipc']);
    
    this._dialogs = new DialogService(logger, this._ipc);
    this.registerService('dialogs', this._dialogs, ['ipc']);
    
    this._editors = new EditorService(logger, this._ipc);
    this.registerService('editors', this._editors, ['ipc']);
    
    this._git = new GitService(logger, this._ipc);
    this.registerService('git', this._git, ['ipc']);
    
    // Update CommandService dependency
    this._commands = new CommandService(
      logger,
      this._context,
      this._keybinding,
      this._ipc,
      this._configuration, // Use configuration
      this._editors
    );
    this.registerService('commands', this._commands, ['context', 'keybinding', 'ipc', 'configuration']); // Updated dependencies
    
    // MenuService now gets WindowManager via setWindowManager
    this._menus = new MenuService(logger, this._commands, this._context, this._ipc, () => this._windowManager); // Pass getter function
    this.registerService('menus', this._menus, ['commands', 'context', 'ipc']);
    
    // Update AIService dependency
    this._ai = new AIService(logger, this._ipc, this._configuration); // Use configuration
    this.registerService('ai', this._ai, ['configuration', 'commands']); // Updated dependencies
    
    // Health monitoring service
    this._health = new HealthService(logger);
    this.registerService('health', this._health);

    // Resource monitoring service
    this._resourceMonitor = new ResourceMonitorService(logger);
    this.registerService('resourceMonitor', this._resourceMonitor);

    this.logger.info("ServiceManager: Core services instantiated.");
  }
  
  // Added configuration getter
  public get configuration(): ConfigurationServiceAPI {
    return this._configuration;
  }
  
  // --- Public accessors ---
  // Removed settings getter
  
  public get storage(): StorageServiceAPI {
    return this._storage;
  }
  
  public get commands(): CommandServiceAPI {
    return this._commands;
  }
  
  public get context(): ContextServiceAPI {
    return this._context;
  }
  
  public get views(): ViewServiceAPI {
    return this._views;
  }
  
  public get ipc(): IpcServiceAPI {
    return this._ipc;
  }
  
  public get notifications(): NotificationServiceAPI {
    return this._notifications;
  }
  
  public get dialogs(): DialogServiceAPI {
    return this._dialogs;
  }
  
  public get menus(): MenuServiceAPI {
    return this._menus;
  }
  
  public get editors(): EditorServiceAPI {
    return this._editors;
  }
  
  public get git(): GitServiceAPI {
    return this._git;
  }
  
  public get keybinding(): KeybindingServiceAPI {
    return this._keybinding;
  }
  
  public get ai(): AIServiceAPI {
    return this._ai;
  }
  
  public get lifecycle(): AppLifecycle {
    return this._lifecycle;
  }

  public get health(): HealthService {
    return this._health;
  }

  public get resourceMonitor(): ResourceMonitorServiceAPI {
    return this._resourceMonitor;
  }
  
  /**
   * Sets the WindowManager instance after it has been created.
   * @param windowManager The WindowManager instance.
   */
  public setWindowManager(windowManager: WindowManager): void {
    this._windowManager = windowManager;
    this.logger.info("WindowManager instance set in ServiceManager.");
    // Potentially update services that need the window manager instance now
    // e.g., this._menus.updateWindowManager(windowManager); if MenuService needs it immediately
  }
  
  /**
   * Инициализирует сервисы, которые требуют специальной настройки после создания главного окна.
   * Позволяет повторную инициализацию для сервисов, зависящих от окна.
   * @param getCurrentWindow Функция для получения текущего экземпляра главного окна BrowserWindow.
   */
  public async initializeServices(
    getCurrentWindow: () => BrowserWindow | null
  ): Promise<void> {
    // Генерируем соответствующее событие жизненного цикла в зависимости от состояния инициализации
    if (!this.isInitialized) {
      this.logger.info('Initializing core services for the first time...');
      await this._lifecycle.emit(AppLifecycleEvent.WINDOW_CREATED);
    } else {
      this.logger.info('Re-initializing window-dependent core services...');
    }
    
    // Получаем все сервисы из реестра
    const services = this.serviceRegistry.getAllServices();
    // Инициализируем сервисы, используя стандартный интерфейс
    const initPromises = services.map(async (service) => {
      try {
        await service.initialize(getCurrentWindow);
        return true;
      } catch (error) {
        this.logger.error(`Error initializing service ${service.constructor?.name || 'unknown'}:`, error);
        return false;
      }
    });
    
    // Ожидаем завершения всех инициализаций
    await Promise.allSettled(initPromises);
    
    // Setup health monitoring after services are initialized
    if (!this.isInitialized) {
      await this.setupHealthMonitoring();
    }
    
    // Обновляем состояние инициализации и генерируем событие, если это первая инициализация
    if (!this.isInitialized) {
      this.isInitialized = true;
      await this._lifecycle.emit(AppLifecycleEvent.SERVICES_INITIALIZED);
    }
    
    this.logger.info('Core services initialization/re-initialization complete.');
  }
  
  /**
   * Собирает API всех основных сервисов для предоставления.
   * @returns Объект, содержащий API основных сервисов.
   */
  public getCoreServicesAPI(): CoreServicesAPI {
    // Возвращаем внутренне управляемые экземпляры, предоставляя типы API или классы
    return {
      commands: this._commands,
      views: this._views,
      storage: this._storage,
      configuration: this._configuration, // Changed from settings
      ipc: this._ipc,
      notifications: this._notifications,
      logger: this.logger,
      dialogs: this._dialogs,
      context: this._context,
      menus: this._menus,
      editors: this._editors,
      git: this._git,
      ai: this._ai,
      lifecycle: this._lifecycle,
      health: this._health,
      resourceMonitor: this._resourceMonitor,
    };
  }
  
  /**
   * Освобождает все зарегистрированные сервисы.
   */
  public async dispose(): Promise<void> {
    if (this.isDisposed) {
      return;
    }
    
    this.logger.info('Disposing ServiceManager and all registered services...');
    try {
      // Dispose health IPC handlers
      if (this._healthIpcHandlers) {
        this._healthIpcHandlers.dispose();
        this._healthIpcHandlers = null;
      }
      
      await this.serviceRegistry.dispose();
      this.isInitialized = false;
      this.logger.info('ServiceManager disposed.');
    } catch (err) {
      this.logger.error('Error during service disposal:', err);
    }
  }
  
  /**
   * Setup health monitoring for all core services
   */
  private async setupHealthMonitoring(): Promise<void> {
    this.logger.info('Setting up health monitoring for core services...');
    
    // Register services with health checkers
    this._health.registerService(
      'storage',
      this._storage,
      new StorageHealthChecker(this._storage),
      {interval: 30000, timeout: 5000, retryAttempts: 3, restartThreshold: 5, enabled: true}
    );
    
    this._health.registerService(
      'git',
      this._git,
      new GitHealthChecker(this._git),
      {interval: 60000, timeout: 10000, retryAttempts: 2, restartThreshold: 3, enabled: true}
    );
    
    this._health.registerService(
      'ipc',
      this._ipc,
      new IpcHealthChecker(this._ipc),
      {interval: 30000, timeout: 5000, retryAttempts: 3, restartThreshold: 5, enabled: true}
    );
    
    this._health.registerService(
      'logging',
      this.logger,
      new LoggingHealthChecker(this.logger),
      {interval: 60000, timeout: 3000, retryAttempts: 2, restartThreshold: 3, enabled: true}
    );
    
    // Register system health checkers
    this._health.registerService(
      'memory',
      this._health, // Use health service as placeholder
      new MemoryHealthChecker(),
      {interval: 30000, timeout: 1000, retryAttempts: 1, restartThreshold: 10, enabled: true}
    );
    
    this._health.registerService(
      'filesystem',
      this._health, // Use health service as placeholder
      new FileSystemHealthChecker([process.cwd()]),
      {interval: 60000, timeout: 5000, retryAttempts: 2, restartThreshold: 5, enabled: true}
    );
    
    // Start health monitoring
    this._health.startMonitoring();
    
    // Setup health IPC handlers
    this._healthIpcHandlers = new HealthIpcHandlers(this._ipc, this._health, this.logger);
    this._healthIpcHandlers.startHealthBroadcasting(30000); // Broadcast every 30 seconds
    
    this.logger.info('Health monitoring started for all core services');
  }
  
  /**
   * Регистрирует сервис в реестре и контейнере зависимостей
   * @param name Имя сервиса
   * @param serviceInstance Экземпляр сервиса
   * @param dependencies Имена сервисов, от которых зависит этот сервис
   * @returns Зарегистрированный экземпляр сервиса
   */
  private registerService<T extends IBaseService>(name: string, serviceInstance: T, dependencies: string[] = []): T {
    // Регистрируем в реестре сервисов
    this.serviceRegistry.registerService(name, serviceInstance, dependencies);
    return serviceInstance;
  }
}

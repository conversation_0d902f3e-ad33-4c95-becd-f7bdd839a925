import {LoggingServiceAPI} from '@services/logging.service';
import {IBaseService} from "@services/base.service";
import {Disposable} from '@shared/types/common';

/**
 * Interface for service registration information
 */
export interface ServiceRegistration<T extends IBaseService = IBaseService> {
  instance: T;
  name: string;
  dependencies: string[];
  factory?: () => T;
  isRestartable: boolean;
}

/**
 * Manages the registration and lifecycle of services
 */
export class ServiceRegistry implements Disposable {
  private services = new Map<string, ServiceRegistration>();
  private logger: LoggingServiceAPI;
  private isDisposed = false;
  
  constructor(logger: LoggingServiceAPI) {
    this.logger = logger;
    this.logger.info('ServiceRegistry created');
  }
  
  /**
   * Registers a service with the registry
   * @param name Service name
   * @param instance Service instance
   * @param dependencies Names of services this service depends on
   * @param factory Optional factory function to recreate the service
   * @returns The registered service instance
   */
  public registerService<T extends IBaseService>(
    name: string,
    instance: T,
    dependencies: string[] = [],
    factory?: () => T
  ): T {
    if (this.isDisposed) {
      throw new Error('Cannot register service: ServiceRegistry has been disposed');
    }
    
    if (this.services.has(name)) {
      this.logger.warn(`Service '${name}' is already registered. Overwriting.`);
    }
    
    const registration: ServiceRegistration<T> = {
      instance,
      name,
      dependencies,
      factory,
      isRestartable: !!factory
    };
    
    this.services.set(name, registration);
    return instance;
  }
  
  /**
   * Gets all registered services
   * @returns Array of all service instances
   */
  public getAllServices(): IBaseService[] {
    return Array.from(this.services.values()).map(reg => reg.instance);
  }
  
  /**
   * Disposes all services in the registry in reverse dependency order
   */
  public async dispose(): Promise<void> {
    if (this.isDisposed) {
      return;
    }

    this.logger.info('Disposing ServiceRegistry and all registered services...');
    this.isDisposed = true;

    // Get services in reverse order to dispose dependents before dependencies
    const servicesArray = Array.from(this.services.values()).reverse();

    // Dispose services sequentially to avoid dependency issues
    for (const registration of servicesArray) {
      try {
        this.logger.debug(`Disposing service: ${registration.name}`);

        // Check if service has dispose method and call it
        if (registration.instance && typeof registration.instance.dispose === 'function') {
          await registration.instance.dispose();
          this.logger.debug(`Successfully disposed service: ${registration.name}`);
        } else {
          this.logger.warn(`Service ${registration.name} does not implement dispose method`);
        }
      } catch (error) {
        this.logger.error(`Error disposing service ${registration.name}:`, error);
        // Continue disposing other services even if one fails
      }
    }

    // Clear the services map
    this.services.clear();
    this.logger.info('ServiceRegistry disposed successfully');
  }
}

import { LoggingServiceAPI } from './logging.service'; // Import API type
import {IpcServiceAPI} from './ipc.service';
import {BaseService, IBaseService} from "./base.service";
import {
  GitStatus,
  GitCommit,
  GitDiff,
  GitCommandOptions,
  GitFileStatus,
  GitFileStatusCode,
  CommitHash
} from '@shared/types/git';
import { simpleGit, SimpleGit, StatusResult } from 'simple-git';
import * as path from 'path';
import * as fs from 'fs/promises';
import { createGitError, NotFoundError } from '@shared/errors';

/**
 * API for interacting with Git repositories.
 */
export interface GitServiceAPI extends IBaseService {
  /**
   * Initializes a Git repository in the specified directory if it doesn't exist.
   * @param projectPath The absolute path to the project directory.
   */
  initRepo(projectPath: string): Promise<void>;

  /**
   * Gets the status of the Git repository.
   * @param repoPath The absolute path to the repository directory.
   */
  status(repoPath: string): Promise<GitStatus>;

  /**
   * Stages one or more files for commit.
   * @param repoPath The absolute path to the repository directory.
   * @param files Path(s) relative to the repoPath.
   */
  stage(repoPath: string, files: string | string[]): Promise<void>;

  /**
   * Commits staged changes.
   * @param repoPath The absolute path to the repository directory.
   * @param message The commit message.
   */
  commit(repoPath: string, message: string): Promise<void>;

  /**
   * Gets the commit history.
   * @param repoPath The absolute path to the repository directory.
   * @param options Optional: max count, file path, etc.
   */
  log(repoPath: string, options?: GitCommandOptions): Promise<GitCommit[]>;

  /**
   * Gets the diff for specified files or paths.
   * @param repoPath The absolute path to the repository directory.
   * @param options Optional: file path, commit range, etc.
   */
  diff(repoPath: string, options?: GitCommandOptions): Promise<GitDiff[]>;

  /**
   * Removes file(s) from the working tree and the index.
   * @param repoPath The absolute path to the repository directory.
   * @param files Path(s) relative to the repoPath.
    */
   remove(repoPath: string, files: string | string[]): Promise<void>;

   /**
    * Moves or renames a file within the repository's index (git mv).
    * Optional because not all libraries might support it easily, fallback exists.
    * @param repoPath The absolute path to the repository directory.
    * @param oldPath Path relative to repoPath.
    * @param newPath Path relative to repoPath.
    */
   mv?(repoPath: string, oldPath: string, newPath: string): Promise<void>; // Make optional

   // Add other necessary methods like checkout, branch, merge, etc.

   // Additional methods used by extensions (for compatibility)
   getStatus?(repositoryPath: string): Promise<GitStatus>; // Alias for status
   add?(repositoryPath: string, files: string | string[]): Promise<void>; // Alias for stage
   init?(repositoryPath: string): Promise<void>; // Alias for initRepo
   clone?(url: string, targetPath: string): Promise<void>;
   getBranches?(repositoryPath: string): Promise<string[]>;
   getCurrentBranch?(repositoryPath: string): Promise<string>;
   checkoutBranch?(repositoryPath: string, branchName: string): Promise<void>;
 }

/**
 * Service for interacting with Git repositories.
 * Implements actual Git functionality using simple-git library.
 */
export class GitService extends BaseService implements GitServiceAPI {
  private readonly ipcService: IpcServiceAPI;
  private readonly gitInstances = new Map<string, SimpleGit>();

  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, "Git Service");
    this.ipcService = ipcService;
  }

  /**
   * Gets or creates a SimpleGit instance for the given repository path
   */
  private getGitInstance(repoPath: string): SimpleGit {
    const normalizedPath = path.resolve(repoPath);

    if (!this.gitInstances.has(normalizedPath)) {
      const git = simpleGit(normalizedPath);
      this.gitInstances.set(normalizedPath, git);
      this.logger.debug(`[GitService] Created Git instance for: ${normalizedPath}`);
    }

    const instance = this.gitInstances.get(normalizedPath);
    if (!instance) {
      throw new Error(`Git instance not found for path: ${normalizedPath}`);
    }
    return instance;
  }

  /**
   * Validates that a path exists and is accessible
   */
  private async validatePath(repoPath: string): Promise<void> {
    try {
      await fs.access(repoPath);
    } catch (_error) {
      throw new NotFoundError('Repository path', repoPath, { source: 'GitService' });
    }
  }

  /**
   * Converts simple-git status to our GitStatus format
   */
  private convertStatus(status: StatusResult): GitStatus {
    const staged: GitFileStatus[] = [];
    const unstaged: GitFileStatus[] = [];

    // Process staged files
    status.staged.forEach(file => {
      staged.push({
        path: file,
        status: this.getFileStatusCode(status, file, true)
      });
    });

    // Process modified files
    status.modified.forEach(file => {
      unstaged.push({
        path: file,
        status: 'modified'
      });
    });

    // Process deleted files
    status.deleted.forEach(file => {
      unstaged.push({
        path: file,
        status: 'deleted'
      });
    });

    // Process renamed files
    status.renamed.forEach(rename => {
      staged.push({
        path: rename.to,
        status: 'renamed'
      });
    });

    return {
      branch: status.current || 'unknown',
      ahead: status.ahead || 0,
      behind: status.behind || 0,
      staged,
      unstaged,
      untracked: [...status.not_added]
    };
  }

  /**
   * Determines the file status code based on simple-git status
   */
  private getFileStatusCode(status: StatusResult, filePath: string, _isStaged: boolean): GitFileStatusCode {
    if (status.created.includes(filePath)) return 'added';
    if (status.modified.includes(filePath)) return 'modified';
    if (status.deleted.includes(filePath)) return 'deleted';
    if (status.renamed.some(r => r.to === filePath || r.from === filePath)) return 'renamed';
    if (status.not_added.includes(filePath)) return 'untracked';
    return 'unmodified';
  }

  async initRepo(projectPath: string): Promise<void> {
    this.logger.info(`[GitService] Initializing Git repository at: ${projectPath}`);

    try {
      // Ensure the directory exists
      await fs.mkdir(projectPath, { recursive: true });

      const git = this.getGitInstance(projectPath);
      const isRepo = await git.checkIsRepo();

      if (!isRepo) {
        await git.init();
        this.logger.info(`[GitService] Successfully initialized Git repository at: ${projectPath}`);

        // Create initial .gitignore if it doesn't exist
        await this.createInitialGitignore(projectPath);

        // Set default branch to main if possible
        try {
          await git.checkoutLocalBranch('main');
          this.logger.debug(`[GitService] Set default branch to 'main' for: ${projectPath}`);
        } catch (branchError) {
          this.logger.debug(`[GitService] Could not set default branch to 'main', using default: ${branchError}`);
        }
      } else {
        this.logger.info(`[GitService] Git repository already exists at: ${projectPath}`);
      }
    } catch (error) {
      this.logger.error(`[GitService] Failed to initialize Git repository at ${projectPath}:`, error);
      throw createGitError('init', projectPath, 'Failed to initialize Git repository', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Creates an initial .gitignore file for the repository
   */
  private async createInitialGitignore(projectPath: string): Promise<void> {
    const gitignorePath = path.join(projectPath, '.gitignore');

    try {
      // Check if .gitignore already exists
      await fs.access(gitignorePath);
      this.logger.debug(`[GitService] .gitignore already exists at: ${gitignorePath}`);
    } catch {
      // Create default .gitignore
      const defaultGitignore = `# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
.cache/

# Node modules (if any)
node_modules/

# Logs
*.log
logs/
`;

      await fs.writeFile(gitignorePath, defaultGitignore, 'utf-8');
      this.logger.debug(`[GitService] Created default .gitignore at: ${gitignorePath}`);
    }
  }

  async status(repoPath: string): Promise<GitStatus> {
    this.logger.debug(`[GitService] Getting status for repository: ${repoPath}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw createGitError('status', repoPath, 'Not a Git repository');
      }

      const status = await git.status();
      const gitStatus = this.convertStatus(status);

      this.logger.debug(`[GitService] Status retrieved for ${repoPath}: ${gitStatus.staged.length} staged, ${gitStatus.unstaged.length} unstaged, ${gitStatus.untracked.length} untracked`);
      return gitStatus;
    } catch (error) {
      this.logger.error(`[GitService] Failed to get status for ${repoPath}:`, error);
      throw createGitError('status', repoPath, 'Failed to get Git status', error instanceof Error ? error : undefined);
    }
  }

  async stage(repoPath: string, files: string | string[]): Promise<void> {
    const filesToStage = Array.isArray(files) ? files : [files];
    this.logger.debug(`[GitService] Staging files in ${repoPath}: ${filesToStage.join(', ')}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw createGitError('stage', repoPath, 'Not a Git repository');
      }

      await git.add(filesToStage);
      this.logger.info(`[GitService] Successfully staged ${filesToStage.length} file(s) in ${repoPath}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to stage files in ${repoPath}:`, error);
      throw createGitError('stage', repoPath, 'Failed to stage files', error instanceof Error ? error : undefined);
    }
  }

  async commit(repoPath: string, message: string): Promise<void> {
    this.logger.debug(`[GitService] Committing changes in ${repoPath} with message: "${message}"`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Not a Git repository: ${repoPath}`);
      }

      // Check if there are staged changes
      const status = await git.status();
      if (status.staged.length === 0) {
        this.logger.warn(`[GitService] No staged changes to commit in ${repoPath}`);
        return;
      }

      const result = await git.commit(message);
      this.logger.info(`[GitService] Successfully committed changes in ${repoPath}. Commit: ${result.commit}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to commit changes in ${repoPath}:`, error);
      throw new Error(`Failed to commit changes: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async log(repoPath: string, options?: GitCommandOptions): Promise<GitCommit[]> {
    this.logger.debug(`[GitService] Getting commit log for ${repoPath}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Not a Git repository: ${repoPath}`);
      }

      // Build log options
      const logOptions: { maxCount?: number } = {};
      if (options?.maxBuffer) {
        logOptions.maxCount = Math.min(options.maxBuffer, 1000); // Limit to reasonable number
      }

      const logResult = await git.log(logOptions);

      const commits: GitCommit[] = logResult.all.map(commit => ({
        hash: commit.hash as CommitHash,
        shortHash: commit.hash.substring(0, 7),
        message: commit.message,
        author: commit.author_name,
        email: commit.author_email,
        date: new Date(commit.date)
      }));

      this.logger.debug(`[GitService] Retrieved ${commits.length} commits from ${repoPath}`);
      return commits;
    } catch (error) {
      this.logger.error(`[GitService] Failed to get commit log for ${repoPath}:`, error);
      throw new Error(`Failed to get commit log: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async diff(repoPath: string, _options?: GitCommandOptions): Promise<GitDiff[]> {
    this.logger.debug(`[GitService] Getting diff for ${repoPath}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Not a Git repository: ${repoPath}`);
      }

      // Get diff summary for file-level changes
      const diffSummary = await git.diffSummary();

      const diffs: GitDiff[] = diffSummary.files.map(file => ({
        path: file.file,
        status: this.mapDiffStatus(file.binary),
        additions: (file as any).insertions || 0,
        deletions: (file as any).deletions || 0,
        hunks: [] // For now, we don't parse detailed hunks
      }));

      this.logger.debug(`[GitService] Retrieved diff for ${diffs.length} files from ${repoPath}`);
      return diffs;
    } catch (error) {
      this.logger.error(`[GitService] Failed to get diff for ${repoPath}:`, error);
      throw new Error(`Failed to get diff: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Maps diff file status to our GitFileStatusCode
   */
  private mapDiffStatus(isBinary: boolean): GitFileStatusCode {
    // This is a simplified mapping - in a real implementation,
    // we'd need more detailed diff parsing to determine exact status
    return isBinary ? 'modified' : 'modified';
  }

  async remove(repoPath: string, files: string | string[]): Promise<void> {
    const filesToRemove = Array.isArray(files) ? files : [files];
    this.logger.debug(`[GitService] Removing files from Git index in ${repoPath}: ${filesToRemove.join(', ')}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Not a Git repository: ${repoPath}`);
      }

      // Remove files from Git index (but keep in working directory)
      await git.rm(filesToRemove);
      this.logger.info(`[GitService] Successfully removed ${filesToRemove.length} file(s) from Git index in ${repoPath}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to remove files from Git index in ${repoPath}:`, error);
      throw new Error(`Failed to remove files from Git index: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async mv(repoPath: string, oldPath: string, newPath: string): Promise<void> {
    this.logger.debug(`[GitService] Moving file in Git index: ${oldPath} -> ${newPath} in ${repoPath}`);

    try {
      await this.validatePath(repoPath);
      const git = this.getGitInstance(repoPath);

      // Check if it's a valid Git repository
      const isRepo = await git.checkIsRepo();
      if (!isRepo) {
        throw new Error(`Not a Git repository: ${repoPath}`);
      }

      // Use git mv to move/rename file
      await git.mv(oldPath, newPath);
      this.logger.info(`[GitService] Successfully moved file in Git index: ${oldPath} -> ${newPath} in ${repoPath}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to move file in Git index: ${oldPath} -> ${newPath} in ${repoPath}:`, error);
      throw new Error(`Failed to move file in Git index: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Alias methods for compatibility with extensions
  async getStatus(repositoryPath: string): Promise<GitStatus> {
    return this.status(repositoryPath);
  }

  async add(repositoryPath: string, files: string | string[]): Promise<void> {
    return this.stage(repositoryPath, files);
  }

  async init(repositoryPath: string): Promise<void> {
    return this.initRepo(repositoryPath);
  }

  async clone(url: string, targetPath: string): Promise<void> {
    this.logger.debug(`[GitService] Cloning repository from ${url} to ${targetPath}`);
    try {
      const git = simpleGit();
      await git.clone(url, targetPath);
      this.logger.info(`[GitService] Successfully cloned repository from ${url} to ${targetPath}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to clone repository from ${url}:`, error);
      throw new Error(`Failed to clone repository: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async getBranches(repositoryPath: string): Promise<string[]> {
    this.logger.debug(`[GitService] Getting branches for ${repositoryPath}`);
    try {
      await this.validatePath(repositoryPath);
      const git = this.getGitInstance(repositoryPath);
      const branches = await git.branch();
      return branches.all;
    } catch (error) {
      this.logger.error(`[GitService] Failed to get branches for ${repositoryPath}:`, error);
      throw new Error(`Failed to get branches: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async getCurrentBranch(repositoryPath: string): Promise<string> {
    this.logger.debug(`[GitService] Getting current branch for ${repositoryPath}`);
    try {
      await this.validatePath(repositoryPath);
      const git = this.getGitInstance(repositoryPath);
      const branches = await git.branch();
      return branches.current;
    } catch (error) {
      this.logger.error(`[GitService] Failed to get current branch for ${repositoryPath}:`, error);
      throw new Error(`Failed to get current branch: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async checkoutBranch(repositoryPath: string, branchName: string): Promise<void> {
    this.logger.debug(`[GitService] Checking out branch ${branchName} in ${repositoryPath}`);
    try {
      await this.validatePath(repositoryPath);
      const git = this.getGitInstance(repositoryPath);
      await git.checkout(branchName);
      this.logger.info(`[GitService] Successfully checked out branch ${branchName} in ${repositoryPath}`);
    } catch (error) {
      this.logger.error(`[GitService] Failed to checkout branch ${branchName} in ${repositoryPath}:`, error);
      throw new Error(`Failed to checkout branch: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Cleanup method to dispose of Git instances and resources
   */
  public async dispose(): Promise<void> {
    this.logger.info('[GitService] Disposing Git service and cleaning up instances');

    // Clear all Git instances
    this.gitInstances.clear();

    await super.dispose();
    this.logger.info('[GitService] Git service disposed');
  }
}

import {Disposable} from '@shared/types/common';
import {ContextServiceAPI} from './context.service';
import {LoggingServiceAPI} from './logging.service';
import {IpcChannels} from '@shared/constants/ipc-channels';
import {IpcServiceAPI} from './ipc.service';
import {KeybindingsGetActiveResult} from '@shared/types/ipc';
import {BrowserWindow} from "electron";
import {BaseService, IBaseService} from "@services/base.service";

interface Keybinding {
  key: string; // например, 'Cmd+S', 'Ctrl+Shift+P'
  command: string; // ID команды
  when?: string; // Контекст, когда привязка активна
}

// Тип для данных, отправляемых в Renderer
type RendererKeybinding = Pick<Keybinding, 'key' | 'command'>;

export interface KeybindingServiceAPI extends IBaseService {
  registerKeybinding(binding: Keybinding): Disposable;
  getKeybindingsForRenderer(): Promise<RendererKeybinding[]>;
  getActiveKeybindingsForCommand(commandId: string): string[];
}

export class KeybindingService extends BaseService implements KeybindingServiceAPI {
  private keybindings: Keybinding[] = [];
  private readonly contextService: ContextServiceAPI;
  private readonly ipcService: IpcServiceAPI;
  
  constructor(logger: LoggingServiceAPI, contextService: ContextServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'KeybindingService');
    this.contextService = contextService;
    this.ipcService = ipcService;
  }
  
  /**
   * Регистрирует комбинацию клавиш для команды.
   * @param binding Объект привязки клавиш.
   * @returns Disposable для удаления привязки.
   */
  registerKeybinding(binding: Keybinding): Disposable {
    if (!binding || !binding.key || !binding.command) {
      this.logger.error('Попытка зарегистрировать неверную привязку клавиш.', binding);
      return {
        dispose: async () => {
          // Пустая операция
        }
      };
    }
    this.logger.info(`Регистрация привязки клавиш: ${binding.key} -> ${binding.command} (when: ${binding.when || 'всегда'})`);
    this.keybindings.push(binding);
    
    return {
      dispose: async () => {
        this.keybindings = this.keybindings.filter(kb => kb !== binding);
      }
    };
  }
  
  async initialize(getCurrentWindowFunc: () => (BrowserWindow | null)): Promise<void> {
    await super.initialize(getCurrentWindowFunc);
    this.registerIpcHandlers();
    this.isInitialized = true;
  }
  
  /**
   * Получает активные привязки клавиш для Renderer.
   * @returns Массив активных привязок.
   */
  async getKeybindingsForRenderer(): Promise<RendererKeybinding[]> {
    this.logger.info('Получение активных привязок клавиш для renderer...');
    const activeBindings: RendererKeybinding[] = [];
    for (const binding of this.keybindings) {
      if (this.contextService.evaluate(binding.when)) {
        activeBindings.push({
          key: binding.key,
          command: binding.command,
        });
      }
    }
    this.logger.info(`Возвращение ${activeBindings.length} активных привязок клавиш.`);
    return activeBindings;
  }
  
  /**
   * Получает активные комбинации клавиш для указанной команды.
   * @param commandId ID команды.
   * @returns Массив строк с комбинациями клавиш.
   */
  getActiveKeybindingsForCommand(commandId: string): string[] {
    const activeCommandBindings: string[] = [];
    for (const binding of this.keybindings) {
      if (binding.command === commandId && this.contextService.evaluate(binding.when)) {
        activeCommandBindings.push(binding.key);
      }
    }
    return activeCommandBindings;
  }
  
  public async dispose() {
    this.logger.info('Disposing KeybindingService...');

    // Remove IPC handlers
    try {
      this.ipcService.removeHandler(IpcChannels.KEYBINDINGS_GET_ACTIVE);
    } catch (error) {
      this.logger.warn('Error removing IPC handlers during disposal:', error);
    }

    // Clear keybindings
    this.keybindings = [];
    this.logger.info('Cleared all keybindings');

    // Call parent dispose
    await super.dispose();
    this.logger.info('KeybindingService disposed successfully');
  }
  
  private registerIpcHandlers(): void {
    this.logger.info('Регистрация обработчиков IPC для KeybindingService...');
    
    this.ipcService.handle<never, KeybindingsGetActiveResult>(
      IpcChannels.KEYBINDINGS_GET_ACTIVE,
      async () => {
        return await this.getKeybindingsForRenderer();
      }
    );
    
    this.logger.info('Обработчики IPC для KeybindingService зарегистрированы.');
  }
}

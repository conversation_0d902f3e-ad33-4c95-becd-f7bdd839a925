/**
 * Dependency Injection Container for service management
 * Provides automatic dependency resolution, lifecycle management, and testing support
 */

import { LoggingServiceAPI } from './logging.service';
import { IBaseService } from './base.service';
import { createServiceError, ErrorSeverity } from '@shared/errors';

/**
 * Service lifecycle states
 */
export enum ServiceLifecycle {
  REGISTERED = 'registered',
  CREATING = 'creating',
  CREATED = 'created',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  DISPOSING = 'disposing',
  DISPOSED = 'disposed',
  ERROR = 'error'
}

/**
 * Service registration options
 */
export interface ServiceRegistrationOptions {
  /** Service lifecycle management */
  lifecycle?: 'singleton' | 'transient' | 'scoped';
  /** Whether the service can be restarted on failure */
  restartable?: boolean;
  /** Service initialization priority (lower numbers initialize first) */
  priority?: number;
  /** Whether to lazy-load the service */
  lazy?: boolean;
  /** Service tags for grouping and filtering */
  tags?: string[];
}

/**
 * Service factory function type
 */
export type ServiceFactory<T = any> = (container: DependencyInjectionContainer) => T | Promise<T>;

/**
 * Service constructor type
 */
export type ServiceConstructor<T = any> = new (...args: any[]) => T;

/**
 * Service identifier type
 */
export type ServiceIdentifier<T = any> = string | symbol | ServiceConstructor<T>;

/**
 * Service registration information
 */
export interface ServiceRegistration<T = any> {
  identifier: ServiceIdentifier<T>;
  factory?: ServiceFactory<T>;
  constructor?: ServiceConstructor<T>;
  dependencies: ServiceIdentifier[];
  options: ServiceRegistrationOptions;
  instance?: T;
  lifecycle: ServiceLifecycle;
  error?: Error;
  createdAt?: number;
  lastAccessed?: number;
}

/**
 * Dependency injection container
 */
export class DependencyInjectionContainer {
  private readonly services = new Map<ServiceIdentifier, ServiceRegistration>();
  private readonly logger: LoggingServiceAPI;
  private readonly scopes = new Map<string, Map<ServiceIdentifier, any>>();
  private currentScope: string | null = null;
  private isDisposed = false;

  constructor(logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('DependencyInjectionContainer');
  }

  /**
   * Register a service with the container
   */
  register<T>(
    identifier: ServiceIdentifier<T>,
    factoryOrConstructor: ServiceFactory<T> | ServiceConstructor<T>,
    dependencies: ServiceIdentifier[] = [],
    options: ServiceRegistrationOptions = {}
  ): this {
    this.throwIfDisposed();

    const registration: ServiceRegistration<T> = {
      identifier,
      dependencies,
      options: {
        lifecycle: 'singleton',
        restartable: false,
        priority: 0,
        lazy: false,
        tags: [],
        ...options,
      },
      lifecycle: ServiceLifecycle.REGISTERED,
    };

    // Determine if it's a factory or constructor
    if (typeof factoryOrConstructor === 'function' && factoryOrConstructor.prototype) {
      registration.constructor = factoryOrConstructor as ServiceConstructor<T>;
    } else {
      registration.factory = factoryOrConstructor as ServiceFactory<T>;
    }

    this.services.set(identifier, registration);
    this.logger.debug(`Registered service: ${this.getServiceName(identifier)}`);

    return this;
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    identifier: ServiceIdentifier<T>,
    factoryOrConstructor: ServiceFactory<T> | ServiceConstructor<T>,
    dependencies: ServiceIdentifier[] = []
  ): this {
    return this.register(identifier, factoryOrConstructor, dependencies, { lifecycle: 'singleton' });
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    identifier: ServiceIdentifier<T>,
    factoryOrConstructor: ServiceFactory<T> | ServiceConstructor<T>,
    dependencies: ServiceIdentifier[] = []
  ): this {
    return this.register(identifier, factoryOrConstructor, dependencies, { lifecycle: 'transient' });
  }

  /**
   * Register an existing instance
   */
  registerInstance<T>(identifier: ServiceIdentifier<T>, instance: T): this {
    this.throwIfDisposed();

    const registration: ServiceRegistration<T> = {
      identifier,
      dependencies: [],
      options: { lifecycle: 'singleton' },
      lifecycle: ServiceLifecycle.INITIALIZED,
      instance,
      createdAt: Date.now(),
    };

    this.services.set(identifier, registration);
    this.logger.debug(`Registered instance: ${this.getServiceName(identifier)}`);

    return this;
  }

  /**
   * Resolve a service from the container
   */
  async resolve<T>(identifier: ServiceIdentifier<T>): Promise<T> {
    this.throwIfDisposed();

    const registration = this.services.get(identifier);
    if (!registration) {
      throw createServiceError(
        'DependencyInjectionContainer',
        'resolve',
        `Service not registered: ${this.getServiceName(identifier)}`,
        { severity: ErrorSeverity.HIGH }
      );
    }

    // Update last accessed time
    registration.lastAccessed = Date.now();

    // Handle different lifecycle types
    switch (registration.options.lifecycle) {
      case 'singleton':
        return this.resolveSingleton(registration);
      case 'transient':
        return this.createInstance(registration);
      case 'scoped':
        return this.resolveScoped(registration);
      default:
        throw createServiceError(
          'DependencyInjectionContainer',
          'resolve',
          `Unknown lifecycle: ${registration.options.lifecycle}`,
          { severity: ErrorSeverity.MEDIUM }
        );
    }
  }

  /**
   * Resolve a singleton service
   */
  private async resolveSingleton<T>(registration: ServiceRegistration<T>): Promise<T> {
    if (registration.instance) {
      return registration.instance;
    }

    if (registration.lifecycle === ServiceLifecycle.CREATING) {
      const error = new Error(`Circular dependency detected for service: ${this.getServiceName(registration.identifier)}`);
      error.name = 'CircularDependencyError';
      throw error;
    }

    registration.instance = await this.createInstance(registration);
    return registration.instance;
  }

  /**
   * Resolve a scoped service
   */
  private async resolveScoped<T>(registration: ServiceRegistration<T>): Promise<T> {
    if (!this.currentScope) {
      throw createServiceError(
        'DependencyInjectionContainer',
        'resolveScoped',
        'No active scope for scoped service resolution',
        { severity: ErrorSeverity.MEDIUM }
      );
    }

    let scopeServices = this.scopes.get(this.currentScope);
    if (!scopeServices) {
      scopeServices = new Map();
      this.scopes.set(this.currentScope, scopeServices);
    }

    let instance = scopeServices.get(registration.identifier);
    if (!instance) {
      instance = await this.createInstance(registration);
      scopeServices.set(registration.identifier, instance);
    }

    return instance;
  }

  /**
   * Create a new service instance
   */
  private async createInstance<T>(registration: ServiceRegistration<T>): Promise<T> {
    try {
      registration.lifecycle = ServiceLifecycle.CREATING;

      // Resolve dependencies first
      const dependencies = await this.resolveDependencies(registration.dependencies);

      let instance: T;

      if (registration.factory) {
        // Use factory function
        instance = await registration.factory(this);
      } else if (registration.constructor) {
        // Use constructor
        instance = new registration.constructor(...dependencies);
      } else {
        throw new Error('No factory or constructor provided');
      }

      registration.lifecycle = ServiceLifecycle.CREATED;
      registration.createdAt = Date.now();

      // Initialize if it's a BaseService
      if (instance && typeof (instance as any).initialize === 'function') {
        registration.lifecycle = ServiceLifecycle.INITIALIZING;
        await (instance as any).initialize(() => null); // TODO: Pass proper window getter
        registration.lifecycle = ServiceLifecycle.INITIALIZED;
      } else {
        registration.lifecycle = ServiceLifecycle.INITIALIZED;
      }

      this.logger.debug(`Created service: ${this.getServiceName(registration.identifier)}`);
      return instance;

    } catch (error) {
      registration.lifecycle = ServiceLifecycle.ERROR;
      registration.error = error instanceof Error ? error : new Error(String(error));

      // Re-throw circular dependency errors directly
      if (error instanceof Error && error.name === 'CircularDependencyError') {
        throw error;
      }

      throw createServiceError(
        'DependencyInjectionContainer',
        'createInstance',
        `Failed to create service: ${this.getServiceName(registration.identifier)}`,
        {
          cause: registration.error,
          severity: ErrorSeverity.HIGH,
        }
      );
    }
  }

  /**
   * Resolve service dependencies
   */
  private async resolveDependencies(dependencies: ServiceIdentifier[]): Promise<any[]> {
    const resolved: any[] = [];

    for (const dependency of dependencies) {
      const instance = await this.resolve(dependency);
      resolved.push(instance);
    }

    return resolved;
  }

  /**
   * Check if a service is registered
   */
  isRegistered<T>(identifier: ServiceIdentifier<T>): boolean {
    this.throwIfDisposed();
    return this.services.has(identifier);
  }

  /**
   * Get service registration information
   */
  getRegistration<T>(identifier: ServiceIdentifier<T>): ServiceRegistration<T> | undefined {
    return this.services.get(identifier) as ServiceRegistration<T> | undefined;
  }

  /**
   * Get all registered services
   */
  getAllRegistrations(): ServiceRegistration[] {
    return Array.from(this.services.values());
  }

  /**
   * Get services by tag
   */
  getServicesByTag(tag: string): ServiceRegistration[] {
    return Array.from(this.services.values()).filter(
      registration => registration.options.tags?.includes(tag)
    );
  }

  /**
   * Create a new scope
   */
  createScope(scopeId: string): void {
    this.scopes.set(scopeId, new Map());
    this.logger.debug(`Created scope: ${scopeId}`);
  }

  /**
   * Enter a scope
   */
  enterScope(scopeId: string): void {
    if (!this.scopes.has(scopeId)) {
      this.createScope(scopeId);
    }
    this.currentScope = scopeId;
    this.logger.debug(`Entered scope: ${scopeId}`);
  }

  /**
   * Exit current scope
   */
  exitScope(): void {
    if (this.currentScope) {
      this.logger.debug(`Exited scope: ${this.currentScope}`);
      this.currentScope = null;
    }
  }

  /**
   * Dispose a scope and all its services
   */
  async disposeScope(scopeId: string): Promise<void> {
    const scopeServices = this.scopes.get(scopeId);
    if (!scopeServices) {
      return;
    }

    // Dispose all services in the scope
    for (const [, instance] of scopeServices) {
      if (instance && typeof instance.dispose === 'function') {
        try {
          await instance.dispose();
        } catch (error) {
          this.logger.error(`Error disposing scoped service:`, error);
        }
      }
    }

    this.scopes.delete(scopeId);
    this.logger.debug(`Disposed scope: ${scopeId}`);
  }

  /**
   * Get service name for logging
   */
  private getServiceName(identifier: ServiceIdentifier): string {
    if (typeof identifier === 'string') {
      return identifier;
    }
    if (typeof identifier === 'symbol') {
      return identifier.toString();
    }
    if (typeof identifier === 'function') {
      return identifier.name || 'AnonymousService';
    }
    return 'UnknownService';
  }

  /**
   * Dispose the container and all services
   */
  async dispose(): Promise<void> {
    if (this.isDisposed) {
      return;
    }

    this.logger.info('Disposing dependency injection container...');

    // Dispose all scopes
    for (const scopeId of this.scopes.keys()) {
      await this.disposeScope(scopeId);
    }

    // Dispose all singleton services
    for (const registration of this.services.values()) {
      if (registration.instance && typeof registration.instance.dispose === 'function') {
        try {
          registration.lifecycle = ServiceLifecycle.DISPOSING;
          await registration.instance.dispose();
          registration.lifecycle = ServiceLifecycle.DISPOSED;
        } catch (error) {
          this.logger.error(`Error disposing service ${this.getServiceName(registration.identifier)}:`, error);
        }
      }
    }

    this.services.clear();
    this.scopes.clear();
    this.isDisposed = true;

    this.logger.info('Dependency injection container disposed');
  }

  /**
   * Check if container is disposed
   */
  private throwIfDisposed(): void {
    if (this.isDisposed) {
      throw createServiceError(
        'DependencyInjectionContainer',
        'operation',
        'Container has been disposed',
        { severity: ErrorSeverity.HIGH }
      );
    }
  }
}

import { LoggingServiceAPI } from './logging.service';
import { BaseService, IBaseService } from './base.service';
import { BrowserWindow } from 'electron';

/**
 * Resource usage information
 */
export interface ResourceUsage {
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  timestamp: number;
  activeHandles: number;
  activeRequests: number;
}

/**
 * Resource leak detection result
 */
export interface LeakDetectionResult {
  serviceName: string;
  leaksDetected: boolean;
  issues: string[];
  resourceUsageBefore: ResourceUsage;
  resourceUsageAfter: ResourceUsage;
  memoryLeakMB: number;
  handleLeak: number;
}

/**
 * Service for monitoring resource usage and detecting memory leaks
 */
export interface ResourceMonitorServiceAPI extends IBaseService {
  /**
   * Start monitoring resource usage
   */
  startMonitoring(): void;

  /**
   * Stop monitoring resource usage
   */
  stopMonitoring(): void;

  /**
   * Get current resource usage
   */
  getCurrentResourceUsage(): ResourceUsage;

  /**
   * Test service disposal for resource leaks
   */
  testServiceDisposal(serviceName: string, service: IBaseService): Promise<LeakDetectionResult>;

  /**
   * Get resource usage history
   */
  getResourceHistory(): ResourceUsage[];

  /**
   * Force garbage collection (if available)
   */
  forceGarbageCollection(): void;
}

/**
 * Implementation of resource monitoring service
 */
export class ResourceMonitorService extends BaseService implements ResourceMonitorServiceAPI {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private resourceHistory: ResourceUsage[] = [];
  private readonly maxHistorySize = 100;
  private isMonitoring = false;

  constructor(logger: LoggingServiceAPI) {
    super(logger, 'ResourceMonitorService');
  }

  public async initialize(windowGetter: () => BrowserWindow | null): Promise<void> {
    await super.initialize(windowGetter);
    this.logger.info('ResourceMonitorService initialized');
  }

  /**
   * Start monitoring resource usage
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      this.logger.warn('Resource monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.recordResourceUsage();
    }, 5000); // Record every 5 seconds

    this.logger.info('Resource monitoring started');
  }

  /**
   * Stop monitoring resource usage
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.isMonitoring = false;
    this.logger.info('Resource monitoring stopped');
  }

  /**
   * Get current resource usage
   */
  getCurrentResourceUsage(): ResourceUsage {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      memoryUsage,
      cpuUsage,
      timestamp: Date.now(),
      activeHandles: (process as any)._getActiveHandles?.()?.length || 0,
      activeRequests: (process as any)._getActiveRequests?.()?.length || 0,
    };
  }

  /**
   * Test service disposal for resource leaks
   */
  async testServiceDisposal(serviceName: string, service: IBaseService): Promise<LeakDetectionResult> {
    this.logger.info(`Testing disposal of service: ${serviceName}`);

    // Force garbage collection before test
    this.forceGarbageCollection();
    await this.waitForGC();

    // Record resource usage before disposal
    const resourceUsageBefore = this.getCurrentResourceUsage();

    // Dispose the service
    try {
      await service.dispose();
    } catch (error) {
      this.logger.error(`Error disposing service ${serviceName}:`, error);
    }

    // Force garbage collection after disposal
    this.forceGarbageCollection();
    await this.waitForGC();

    // Record resource usage after disposal
    const resourceUsageAfter = this.getCurrentResourceUsage();

    // Analyze for leaks
    const issues: string[] = [];
    const memoryLeakMB = (resourceUsageAfter.memoryUsage.heapUsed - resourceUsageBefore.memoryUsage.heapUsed) / (1024 * 1024);
    const handleLeak = resourceUsageAfter.activeHandles - resourceUsageBefore.activeHandles;

    // Check for memory leaks (threshold: 1MB)
    if (memoryLeakMB > 1) {
      issues.push(`Memory leak detected: ${memoryLeakMB.toFixed(2)}MB increase`);
    }

    // Check for handle leaks
    if (handleLeak > 0) {
      issues.push(`Handle leak detected: ${handleLeak} handles not cleaned up`);
    }

    // Check for request leaks
    const requestLeak = resourceUsageAfter.activeRequests - resourceUsageBefore.activeRequests;
    if (requestLeak > 0) {
      issues.push(`Request leak detected: ${requestLeak} active requests not cleaned up`);
    }

    const result: LeakDetectionResult = {
      serviceName,
      leaksDetected: issues.length > 0,
      issues,
      resourceUsageBefore,
      resourceUsageAfter,
      memoryLeakMB,
      handleLeak,
    };

    if (result.leaksDetected) {
      this.logger.warn(`Resource leaks detected in ${serviceName}:`, issues);
    } else {
      this.logger.info(`No resource leaks detected in ${serviceName}`);
    }

    return result;
  }

  /**
   * Get resource usage history
   */
  getResourceHistory(): ResourceUsage[] {
    return [...this.resourceHistory];
  }

  /**
   * Force garbage collection (if available)
   */
  forceGarbageCollection(): void {
    if (global.gc) {
      global.gc();
      this.logger.debug('Forced garbage collection');
    } else {
      this.logger.debug('Garbage collection not available (run with --expose-gc)');
    }
  }

  /**
   * Record current resource usage
   */
  private recordResourceUsage(): void {
    const usage = this.getCurrentResourceUsage();
    this.resourceHistory.push(usage);

    // Keep history size manageable
    if (this.resourceHistory.length > this.maxHistorySize) {
      this.resourceHistory.shift();
    }
  }

  /**
   * Wait for garbage collection to complete
   */
  private async waitForGC(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Dispose the resource monitor service
   */
  public async dispose(): Promise<void> {
    this.logger.info('Disposing ResourceMonitorService...');

    // Stop monitoring
    this.stopMonitoring();

    // Clear history
    this.resourceHistory = [];

    // Call parent dispose
    await super.dispose();
    this.logger.info('ResourceMonitorService disposed successfully');
  }
}

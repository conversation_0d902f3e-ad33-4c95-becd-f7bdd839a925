/**
 * Service health monitoring and recovery system
 * Provides health checks, automatic restart capabilities, and service status tracking
 */

import { BaseService, IBaseService } from './base.service';
import { LoggingServiceAPI } from './logging.service';
import { BrowserWindow } from 'electron';
import { createServiceError, ErrorSeverity } from '@shared/errors';

/**
 * Health status levels
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical',
  UNKNOWN = 'unknown'
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  status: HealthStatus;
  message?: string;
  details?: Record<string, unknown>;
  timestamp: number;
  responseTime: number;
}

/**
 * Service health information
 */
export interface ServiceHealth {
  serviceName: string;
  status: HealthStatus;
  lastCheck: number;
  lastHealthy: number;
  consecutiveFailures: number;
  totalChecks: number;
  totalFailures: number;
  averageResponseTime: number;
  lastError?: string;
}

/**
 * Health check configuration
 */
export interface HealthCheckConfig {
  interval: number; // Check interval in milliseconds
  timeout: number; // Health check timeout in milliseconds
  retryAttempts: number; // Number of retry attempts before marking as unhealthy
  restartThreshold: number; // Number of consecutive failures before restart
  enabled: boolean;
}

/**
 * Service health checker interface
 */
export interface IHealthChecker {
  checkHealth(): Promise<HealthCheckResult>;
}

/**
 * Default health check configuration
 */
const DEFAULT_HEALTH_CONFIG: HealthCheckConfig = {
  interval: 30000, // 30 seconds
  timeout: 5000, // 5 seconds
  retryAttempts: 3,
  restartThreshold: 5,
  enabled: true,
};

/**
 * Health monitoring service
 */
export class HealthService extends BaseService {
  private readonly services = new Map<string, IBaseService>();
  private readonly healthCheckers = new Map<string, IHealthChecker>();
  private readonly serviceHealth = new Map<string, ServiceHealth>();
  private readonly healthConfigs = new Map<string, HealthCheckConfig>();
  private readonly checkIntervals = new Map<string, NodeJS.Timeout>();
  private readonly serviceFactories = new Map<string, () => IBaseService>();
  
  private isMonitoring = false;
  private globalHealthScore = 1.0;

  constructor(logger: LoggingServiceAPI) {
    super(logger, 'HealthService');
  }

  /**
   * Initialize the health service
   */
  async initialize(windowGetter: () => BrowserWindow | null): Promise<void> {
    await super.initialize(windowGetter);
    this.logger.info('Health monitoring service initialized');
    this.isInitialized = true;
  }

  /**
   * Register a service for health monitoring
   */
  registerService(
    name: string, 
    service: IBaseService, 
    healthChecker?: IHealthChecker,
    config?: Partial<HealthCheckConfig>,
    factory?: () => IBaseService
  ): void {
    this.throwIfDisposed();
    
    this.services.set(name, service);
    
    if (healthChecker) {
      this.healthCheckers.set(name, healthChecker);
    }
    
    if (factory) {
      this.serviceFactories.set(name, factory);
    }
    
    // Initialize health tracking
    this.serviceHealth.set(name, {
      serviceName: name,
      status: HealthStatus.UNKNOWN,
      lastCheck: 0,
      lastHealthy: Date.now(),
      consecutiveFailures: 0,
      totalChecks: 0,
      totalFailures: 0,
      averageResponseTime: 0,
    });
    
    // Set health check configuration
    const healthConfig = { ...DEFAULT_HEALTH_CONFIG, ...config };
    this.healthConfigs.set(name, healthConfig);
    
    this.logger.info(`Registered service for health monitoring: ${name}`);
  }

  /**
   * Start health monitoring for all registered services
   */
  startMonitoring(): void {
    this.throwIfDisposed();
    
    if (this.isMonitoring) {
      this.logger.warn('Health monitoring is already running');
      return;
    }
    
    this.isMonitoring = true;
    this.logger.info('Starting health monitoring for all services');
    
    for (const [serviceName, config] of this.healthConfigs.entries()) {
      if (config.enabled) {
        this.startServiceMonitoring(serviceName);
      }
    }
  }

  /**
   * Stop health monitoring for all services
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    
    for (const [serviceName, interval] of this.checkIntervals.entries()) {
      clearInterval(interval);
      this.checkIntervals.delete(serviceName);
    }
    
    this.logger.info('Stopped health monitoring for all services');
  }

  /**
   * Start monitoring for a specific service
   */
  private startServiceMonitoring(serviceName: string): void {
    const config = this.healthConfigs.get(serviceName);
    if (!config || !config.enabled) {
      return;
    }
    
    // Clear existing interval if any
    const existingInterval = this.checkIntervals.get(serviceName);
    if (existingInterval) {
      clearInterval(existingInterval);
    }
    
    // Start new monitoring interval
    const interval = setInterval(async () => {
      await this.performHealthCheck(serviceName);
    }, config.interval);
    
    this.checkIntervals.set(serviceName, interval);
    
    // Perform initial health check
    this.performHealthCheck(serviceName).catch(error => {
      this.logger.error(`Initial health check failed for ${serviceName}:`, error);
    });
  }

  /**
   * Perform health check for a specific service
   */
  private async performHealthCheck(serviceName: string): Promise<void> {
    const service = this.services.get(serviceName);
    const healthChecker = this.healthCheckers.get(serviceName);
    const config = this.healthConfigs.get(serviceName);
    const currentHealth = this.serviceHealth.get(serviceName);
    
    if (!service || !config || !currentHealth) {
      return;
    }
    
    const startTime = Date.now();
    let result: HealthCheckResult;
    
    try {
      // Use custom health checker if available, otherwise use default
      if (healthChecker) {
        result = await Promise.race([
          healthChecker.checkHealth(),
          this.createTimeoutPromise(config.timeout)
        ]);
      } else {
        result = await this.defaultHealthCheck(service, config.timeout);
      }
      
      // Update health information
      this.updateServiceHealth(serviceName, result, startTime);
      
      // Handle recovery if service was previously unhealthy
      if (currentHealth.status !== HealthStatus.HEALTHY && result.status === HealthStatus.HEALTHY) {
        this.logger.info(`Service ${serviceName} has recovered`);
        currentHealth.consecutiveFailures = 0;
      }
      
    } catch (error) {
      // Create failed health check result
      result = {
        status: HealthStatus.UNHEALTHY,
        message: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
      
      this.updateServiceHealth(serviceName, result, startTime);
      this.handleServiceFailure(serviceName, error);
    }
    
    // Update global health score
    this.updateGlobalHealth();
  }

  /**
   * Default health check implementation
   */
  private async defaultHealthCheck(service: IBaseService, timeout: number): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    // Basic health check - verify service is not disposed and can respond
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Health check timeout'));
      }, timeout);
      
      try {
        // Check if service is disposed (this should be synchronous)
        if ((service as any).isDisposed) {
          clearTimeout(timeoutId);
          resolve({
            status: HealthStatus.UNHEALTHY,
            message: 'Service is disposed',
            timestamp: Date.now(),
            responseTime: Date.now() - startTime,
          });
          return;
        }
        
        // Service appears healthy
        clearTimeout(timeoutId);
        resolve({
          status: HealthStatus.HEALTHY,
          message: 'Service is responsive',
          timestamp: Date.now(),
          responseTime: Date.now() - startTime,
        });
        
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Create a timeout promise for health checks
   */
  private createTimeoutPromise(timeout: number): Promise<HealthCheckResult> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Health check timeout'));
      }, timeout);
    });
  }

  /**
   * Update service health information
   */
  private updateServiceHealth(serviceName: string, result: HealthCheckResult, startTime: number): void {
    const health = this.serviceHealth.get(serviceName);
    if (!health) return;
    
    health.lastCheck = result.timestamp;
    health.status = result.status;
    health.totalChecks++;
    
    // Update response time average
    const responseTime = result.responseTime;
    health.averageResponseTime = (health.averageResponseTime * (health.totalChecks - 1) + responseTime) / health.totalChecks;
    
    if (result.status === HealthStatus.HEALTHY) {
      health.lastHealthy = result.timestamp;
      health.consecutiveFailures = 0;
    } else {
      health.consecutiveFailures++;
      health.totalFailures++;
      health.lastError = result.message;
    }
    
    this.logger.debug(`Health check for ${serviceName}: ${result.status} (${responseTime}ms)`);
  }

  /**
   * Handle service failure and attempt recovery if needed
   */
  private async handleServiceFailure(serviceName: string, error: unknown): Promise<void> {
    const health = this.serviceHealth.get(serviceName);
    const config = this.healthConfigs.get(serviceName);
    const factory = this.serviceFactories.get(serviceName);
    
    if (!health || !config) return;
    
    this.logger.warn(`Service ${serviceName} health check failed:`, error);
    
    // Check if we should attempt restart
    if (health.consecutiveFailures >= config.restartThreshold && factory) {
      this.logger.error(`Service ${serviceName} has failed ${health.consecutiveFailures} consecutive times. Attempting restart...`);
      
      try {
        await this.restartService(serviceName, factory);
      } catch (restartError) {
        this.logger.error(`Failed to restart service ${serviceName}:`, restartError);
        health.status = HealthStatus.CRITICAL;
      }
    }
  }

  /**
   * Restart a failed service
   */
  private async restartService(serviceName: string, factory: () => IBaseService): Promise<void> {
    const oldService = this.services.get(serviceName);
    
    try {
      // Dispose old service
      if (oldService) {
        await oldService.dispose();
      }
      
      // Create new service instance
      const newService = factory();
      
      // Initialize new service
      if (this.windowGetter) {
        await newService.initialize(this.windowGetter);
      }
      
      // Update service reference
      this.services.set(serviceName, newService);
      
      // Reset health status
      const health = this.serviceHealth.get(serviceName);
      if (health) {
        health.consecutiveFailures = 0;
        health.status = HealthStatus.UNKNOWN; // Will be updated on next health check
      }
      
      this.logger.info(`Successfully restarted service: ${serviceName}`);
      
    } catch (error) {
      throw createServiceError('HealthService', 'restartService', `Failed to restart service ${serviceName}`, {
        cause: error instanceof Error ? error : undefined,
        severity: ErrorSeverity.HIGH,
      });
    }
  }

  /**
   * Update global health score based on all service health
   */
  updateGlobalHealth(): void {
    const healthValues = Array.from(this.serviceHealth.values());
    
    if (healthValues.length === 0) {
      this.globalHealthScore = 1.0;
      return;
    }
    
    let totalScore = 0;
    for (const health of healthValues) {
      switch (health.status) {
        case HealthStatus.HEALTHY:
          totalScore += 1.0;
          break;
        case HealthStatus.DEGRADED:
          totalScore += 0.7;
          break;
        case HealthStatus.UNHEALTHY:
          totalScore += 0.3;
          break;
        case HealthStatus.CRITICAL:
          totalScore += 0.0;
          break;
        case HealthStatus.UNKNOWN:
          totalScore += 0.5;
          break;
      }
    }
    
    this.globalHealthScore = totalScore / healthValues.length;
  }

  /**
   * Get health status for a specific service
   */
  getServiceHealth(serviceName: string): ServiceHealth | undefined {
    this.throwIfDisposed();
    return this.serviceHealth.get(serviceName);
  }

  /**
   * Get health status for all services
   */
  getAllServiceHealth(): Map<string, ServiceHealth> {
    this.throwIfDisposed();
    return new Map(this.serviceHealth);
  }

  /**
   * Get global health score (0.0 to 1.0)
   */
  getGlobalHealthScore(): number {
    this.throwIfDisposed();
    return this.globalHealthScore;
  }

  /**
   * Get overall system health status
   */
  getSystemHealthStatus(): HealthStatus {
    this.throwIfDisposed();
    
    if (this.globalHealthScore >= 0.9) return HealthStatus.HEALTHY;
    if (this.globalHealthScore >= 0.7) return HealthStatus.DEGRADED;
    if (this.globalHealthScore >= 0.3) return HealthStatus.UNHEALTHY;
    return HealthStatus.CRITICAL;
  }

  /**
   * Manually trigger health check for a service
   */
  async checkServiceHealth(serviceName: string): Promise<HealthCheckResult | null> {
    this.throwIfDisposed();

    const healthChecker = this.healthCheckers.get(serviceName);
    const service = this.services.get(serviceName);
    const config = this.healthConfigs.get(serviceName);

    if (!service || !config) {
      return null;
    }

    const startTime = Date.now();
    let result: HealthCheckResult;

    try {
      if (healthChecker) {
        result = await Promise.race([
          healthChecker.checkHealth(),
          this.createTimeoutPromise(config.timeout)
        ]);
      } else {
        result = await this.defaultHealthCheck(service, config.timeout);
      }

      // Update health information after manual check
      this.updateServiceHealth(serviceName, result, startTime);

      return result;
    } catch (error) {
      result = {
        status: HealthStatus.UNHEALTHY,
        message: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };

      // Update health information after failed check
      this.updateServiceHealth(serviceName, result, startTime);

      return result;
    }
  }

  /**
   * Dispose the health service and clean up all resources
   */
  async dispose(): Promise<void> {
    this.logger.info('Disposing HealthService...');

    // Stop monitoring first
    this.stopMonitoring();

    // Clear all monitoring intervals
    if (this.checkIntervals) {
      for (const intervalId of this.checkIntervals.values()) {
        clearInterval(intervalId);
      }
      this.checkIntervals.clear();
    }

    // Clear all collections
    this.services.clear();
    this.healthCheckers.clear();
    this.healthConfigs.clear();
    this.serviceHealth.clear();
    this.serviceFactories.clear();

    // Reset state
    this.isMonitoring = false;
    this.globalHealthScore = 1.0;

    // Call parent dispose
    await super.dispose();
    this.logger.info('HealthService disposed successfully');
  }
}

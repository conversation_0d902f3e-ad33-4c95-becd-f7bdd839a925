import Database from 'better-sqlite3';
import path from 'node:path';
import fs from 'node:fs';
import {app, BrowserWindow} from 'electron';
import {LoggingServiceAPI} from './logging.service';
import {IpcServiceAPI} from './ipc.service';
import {BaseService, IBaseService} from "./base.service";
import {JsonValue} from '@shared/types/common';
import { createStorageError } from '@shared/errors';

/**
 * Connection pool configuration
 */
interface ConnectionPoolConfig {
  maxConnections: number;
  idleTimeout: number; // milliseconds
  acquireTimeout: number; // milliseconds
  statementCacheSize: number;
}

/**
 * Pooled database connection wrapper
 */
interface PooledConnection {
  db: Database.Database;
  inUse: boolean;
  lastUsed: number;
  id: string;
}

/**
 * Prepared statement cache entry
 */
interface CachedStatement {
  statement: Database.Statement;
  lastUsed: number;
  useCount: number;
  connectionId: string;
}

/**
 * Connection pool statistics
 */
interface PoolStatistics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  statementCacheHits: number;
  statementCacheMisses: number;
  statementCacheSize: number;
  averageAcquireTime: number;
}

// --- Generic API Interface ---
// Определяем общий низкоуровневый API для работы с БД
export interface StorageServiceAPI extends IBaseService {
  /**
   * Executes a SQL statement that does not return data (INSERT, UPDATE, DELETE, CREATE, etc.).
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to the RunResult object from better-sqlite3.
   */
  run(sql: string, ...params: unknown[]): Promise<Database.RunResult>;
  
  /**
   * Executes a SQL statement that returns a single row.
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to the row object, or undefined if no row was found.
   */
  get<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T | undefined>;
  
  /**
   * Executes a SQL statement that returns multiple rows.
   * @param sql The SQL statement string. Can contain placeholders (?).
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to an array of row objects.
   */
  all<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T[]>;
  
  /**
   * Executes multiple SQL statements within a transaction.
   * @param callback A function containing the statements to execute transactionally.
   * @returns A promise resolving to the result of the callback function.
   */
  transaction<T>(callback: () => T): Promise<T>; // Убираем аргумент tx из колбэка

  /**
   * Executes a SQL statement directly (for compatibility with extensions)
   * @param sql The SQL statement string.
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to the execution result.
   */
  exec(sql: string, ...params: unknown[]): Promise<Database.RunResult>;

  /**
   * Analyzes query performance and provides optimization recommendations.
   * @param sql The SQL statement to analyze.
   * @param params Parameters to bind to the placeholders.
   * @returns A promise resolving to performance analysis results.
   */
  analyzeQueryPerformance(sql: string, ...params: unknown[]): Promise<{
    queryPlan: any[];
    executionTime: number;
    recommendations: string[];
  }>;

  /**
   * Gets database statistics for monitoring and optimization.
   * @returns A promise resolving to database statistics.
   */
  getDatabaseStatistics(): Promise<{
    tableStats: {
      tableName: string;
      rowCount: number;
      pageCount: number;
      avgRowSize: number;
    }[];
    indexStats: {
      indexName: string;
      tableName: string;
      isUnique: boolean;
      columns: string;
    }[];
    pragmaInfo: {
      journalMode: string;
      foreignKeys: boolean;
      cacheSize: number;
      pageSize: number;
    };
  }>;

  /**
   * Gets connection pool statistics for monitoring and optimization.
   * @returns A promise resolving to connection pool statistics.
   */
  getPoolStatistics(): Promise<PoolStatistics>;

  /**
   * Clears the prepared statement cache.
   * @returns A promise resolving when the cache is cleared.
   */
  clearStatementCache(): Promise<void>;
}

// --------------------


const dbPath = path.join(app.getPath('userData'), 'ai_books_data.sqlite');
// Logging dbPath moved to constructor

// sqliteVerbose function moved inside initializeDatabase where logger is available

// Removed local StorageError class - now using standardized StorageError from shared/errors

export class StorageService extends BaseService implements StorageServiceAPI {
  private connectionPool: PooledConnection[] = [];
  private statementCache = new Map<string, CachedStatement>();
  private readonly ipcService: IpcServiceAPI;
  private readonly poolConfig: ConnectionPoolConfig;
  private poolStats: PoolStatistics;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    super(logger, 'StorageService');
    this.ipcService = ipcService;
    this.poolConfig = {
      maxConnections: 5, // Reasonable for SQLite
      idleTimeout: 30000, // 30 seconds
      acquireTimeout: 5000, // 5 seconds
      statementCacheSize: 100 // Cache up to 100 prepared statements
    };
    this.poolStats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      statementCacheHits: 0,
      statementCacheMisses: 0,
      statementCacheSize: 0,
      averageAcquireTime: 0
    };
    this.logger.info(`Database path: ${dbPath}`);
    this.logger.info(`Connection pool config:`, this.poolConfig);
  }
  
  /**
   * Creates a new database connection with proper configuration
   */
  private createConnection(): Database.Database {
    const db = new Database(dbPath);
    db.pragma('journal_mode = WAL');
    db.pragma('foreign_keys = ON');
    db.pragma('synchronous = NORMAL'); // Better performance for WAL mode
    db.pragma('cache_size = 10000'); // 10MB cache
    db.pragma('temp_store = MEMORY'); // Store temp tables in memory
    return db;
  }

  /**
   * Acquires a connection from the pool
   */
  private async acquireConnection(): Promise<PooledConnection> {
    const startTime = performance.now();

    // Try to find an idle connection
    let connection = this.connectionPool.find(conn => !conn.inUse);

    if (connection) {
      connection.inUse = true;
      connection.lastUsed = Date.now();
      this.poolStats.activeConnections++;
      this.poolStats.idleConnections--;
    } else if (this.connectionPool.length < this.poolConfig.maxConnections) {
      // Create new connection
      const db = this.createConnection();
      connection = {
        db,
        inUse: true,
        lastUsed: Date.now(),
        id: `conn-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
      };
      this.connectionPool.push(connection);
      this.poolStats.totalConnections++;
      this.poolStats.activeConnections++;
      this.logger.debug(`Created new database connection: ${connection.id}`);
    } else {
      // Wait for a connection to become available
      const timeout = this.poolConfig.acquireTimeout;
      const startWait = Date.now();

      while (Date.now() - startWait < timeout) {
        connection = this.connectionPool.find(conn => !conn.inUse);
        if (connection) {
          connection.inUse = true;
          connection.lastUsed = Date.now();
          this.poolStats.activeConnections++;
          this.poolStats.idleConnections--;
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 10)); // Wait 10ms
      }

      if (!connection) {
        throw createStorageError('acquireConnection', 'Failed to acquire database connection within timeout', 'StorageService');
      }
    }

    const acquireTime = performance.now() - startTime;
    this.poolStats.averageAcquireTime = (this.poolStats.averageAcquireTime + acquireTime) / 2;

    return connection;
  }

  /**
   * Releases a connection back to the pool
   */
  private releaseConnection(connection: PooledConnection): void {
    connection.inUse = false;
    connection.lastUsed = Date.now();
    this.poolStats.activeConnections--;
    this.poolStats.idleConnections++;
  }

  /**
   * Gets or creates a cached prepared statement
   */
  private getCachedStatement(sql: string, connection: PooledConnection): Database.Statement {
    const cacheKey = `${connection.id}:${sql}`;
    const cached = this.statementCache.get(cacheKey);

    if (cached) {
      cached.lastUsed = Date.now();
      cached.useCount++;
      this.poolStats.statementCacheHits++;
      return cached.statement;
    }

    // Create new prepared statement
    const statement = connection.db.prepare(sql);

    // Check cache size limit
    if (this.statementCache.size >= this.poolConfig.statementCacheSize) {
      // Remove least recently used statement
      let oldestKey = '';
      let oldestTime = Date.now();

      for (const [key, entry] of this.statementCache.entries()) {
        if (entry.lastUsed < oldestTime) {
          oldestTime = entry.lastUsed;
          oldestKey = key;
        }
      }

      if (oldestKey) {
        this.statementCache.delete(oldestKey);
      }
    }

    // Cache the new statement
    this.statementCache.set(cacheKey, {
      statement,
      lastUsed: Date.now(),
      useCount: 1,
      connectionId: connection.id
    });

    this.poolStats.statementCacheMisses++;
    this.poolStats.statementCacheSize = this.statementCache.size;

    return statement;
  }

  /**
   * Cleans up idle connections and old cached statements
   */
  private cleanupPool(): void {
    const now = Date.now();

    // Remove idle connections that have exceeded timeout
    const connectionsToRemove = this.connectionPool.filter(
      conn => !conn.inUse && (now - conn.lastUsed) > this.poolConfig.idleTimeout
    );

    for (const conn of connectionsToRemove) {
      try {
        conn.db.close();
        this.connectionPool = this.connectionPool.filter(c => c.id !== conn.id);
        this.poolStats.totalConnections--;
        this.poolStats.idleConnections--;
        this.logger.debug(`Closed idle database connection: ${conn.id}`);

        // Remove cached statements for this connection
        for (const [key, cached] of this.statementCache.entries()) {
          if (cached.connectionId === conn.id) {
            this.statementCache.delete(key);
          }
        }
      } catch (error) {
        this.logger.warn(`Error closing idle connection ${conn.id}:`, error);
      }
    }

    this.poolStats.statementCacheSize = this.statementCache.size;
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)) {
    await super.initialize(windowGetter);
    try {
      const dbDir = path.dirname(dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, {recursive: true});
        this.logger.info(`Created database directory: ${dbDir}`);
      }

      // Initialize connection pool with first connection
      const firstConnection = this.createConnection();
      this.connectionPool.push({
        db: firstConnection,
        inUse: false,
        lastUsed: Date.now(),
        id: 'conn-primary'
      });
      this.poolStats.totalConnections = 1;
      this.poolStats.idleConnections = 1;

      this.logger.info('Database connection pool initialized successfully.');
      this.logger.info('WAL mode and foreign keys enabled.');

      // Create performance indexes for query optimization
      await this.createPerformanceIndexes();

      // Start cleanup interval
      this.cleanupInterval = setInterval(() => {
        this.cleanupPool();
      }, 60000); // Cleanup every minute

      this.logger.info('Database connection pool and statement cache ready.');
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to initialize database', error);
      throw new Error(`Failed to initialize database: ${message}`);
    }
  }

  /**
   * Creates performance indexes for frequently queried columns
   * This method is called during database initialization to optimize query performance
   */
  private async createPerformanceIndexes(): Promise<void> {
    try {
      this.logger.info('Creating performance indexes for database optimization...');

      const indexes = [
        // Books table indexes
        'CREATE INDEX IF NOT EXISTS idx_books_created_at ON books(createdAt DESC)',

        // Chapters table indexes
        'CREATE INDEX IF NOT EXISTS idx_chapters_book_id ON chapters(bookId)',
        'CREATE INDEX IF NOT EXISTS idx_chapters_book_position ON chapters(bookId, position ASC)',

        // Scenes table indexes
        'CREATE INDEX IF NOT EXISTS idx_scenes_chapter_id ON scenes(chapterId)',
        'CREATE INDEX IF NOT EXISTS idx_scenes_chapter_position ON scenes(chapterId, position ASC)',
        'CREATE INDEX IF NOT EXISTS idx_scenes_json_file_path ON scenes(jsonFilePath)',

        // Characters table indexes
        'CREATE INDEX IF NOT EXISTS idx_characters_book_id ON characters(bookId)',
        'CREATE INDEX IF NOT EXISTS idx_characters_book_name ON characters(bookId, name ASC)',
        'CREATE INDEX IF NOT EXISTS idx_characters_name ON characters(name ASC)',
        'CREATE INDEX IF NOT EXISTS idx_characters_created_at ON characters(createdAt DESC)',

        // Character events table indexes
        'CREATE INDEX IF NOT EXISTS idx_character_events_character_id ON character_events(characterId)',
        'CREATE INDEX IF NOT EXISTS idx_character_events_book_id ON character_events(bookId)',
        'CREATE INDEX IF NOT EXISTS idx_character_events_scene_id ON character_events(relatedSceneId)',
        'CREATE INDEX IF NOT EXISTS idx_character_events_timeline ON character_events(characterId, timelinePosition ASC)',
        'CREATE INDEX IF NOT EXISTS idx_character_events_scene_timeline ON character_events(relatedSceneId, timelinePosition ASC)',
        'CREATE INDEX IF NOT EXISTS idx_character_events_type ON character_events(eventType)',

        // Character attributes table indexes
        'CREATE INDEX IF NOT EXISTS idx_character_attributes_character_id ON character_attributes(characterId)',
        'CREATE INDEX IF NOT EXISTS idx_character_attributes_key ON character_attributes(characterId, attributeKey)',

        // History log table indexes
        'CREATE INDEX IF NOT EXISTS idx_history_entity ON history_log(entityType, entityId)',
        'CREATE INDEX IF NOT EXISTS idx_history_timestamp ON history_log(timestamp DESC)',
        'CREATE INDEX IF NOT EXISTS idx_history_entity_timestamp ON history_log(entityType, entityId, timestamp DESC)'
      ];

      // Use connection pool for index creation
      const connection = await this.acquireConnection();
      try {
        // Execute all index creation statements
        for (const indexSql of indexes) {
          try {
            connection.db.exec(indexSql);
          } catch (error) {
            // Log but don't fail if index already exists or other non-critical error
            this.logger.warn(`Failed to create index: ${indexSql}`, error);
          }
        }
        this.logger.info(`Successfully created ${indexes.length} performance indexes`);
      } finally {
        this.releaseConnection(connection);
      }
    } catch (error) {
      this.logger.error('Failed to create performance indexes:', error);
      // Don't throw error to avoid breaking database initialization
    }
  }

  // Database operations using connection pool and prepared statement caching
  async run(sql: string, ...params: unknown[]): Promise<Database.RunResult> {
    const connection = await this.acquireConnection();
    try {
      const stmt = this.getCachedStatement(sql, connection);
      const result = stmt.run(...params);
      return result;
    } catch (error: unknown) {
      this.logger.error(`RUN Error: ${sql}`, {params, error});
      throw createStorageError('run', 'Failed to run statement', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }
  
  async get<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T | undefined> {
    const connection = await this.acquireConnection();
    try {
      const stmt = this.getCachedStatement(sql, connection);
      const row = stmt.get(...params) as T | undefined;
      return row;
    } catch (error: unknown) {
      this.logger.error(`GET Error: ${sql}`, {params, error});
      throw createStorageError('get', 'Failed to get row', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }
  
  async all<T = JsonValue>(sql: string, ...params: unknown[]): Promise<T[]> {
    const connection = await this.acquireConnection();
    try {
      const stmt = this.getCachedStatement(sql, connection);
      const rows = stmt.all(...params) as T[];
      return rows;
    } catch (error: unknown) {
      this.logger.error(`ALL Error: ${sql}`, {params, error});
      throw createStorageError('all', 'Failed to get all rows', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }
  
  async transaction<T>(callback: () => T): Promise<T> {
    const connection = await this.acquireConnection();
    try {
      // Create transaction function for this connection
      const txFunction = connection.db.transaction(callback);
      const result = txFunction();
      return result;
    } catch (error: unknown) {
      // better-sqlite3 automatically rolls back transaction on error
      throw createStorageError('transaction', 'Transaction failed', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }

  async exec(sql: string, ...params: unknown[]): Promise<Database.RunResult> {
    // For compatibility, exec is the same as run
    return this.run(sql, ...params);
  }

  /**
   * Analyzes query performance and provides optimization recommendations
   * This method can be used for debugging and monitoring query performance
   */
  async analyzeQueryPerformance(sql: string, ...params: unknown[]): Promise<{
    queryPlan: any[];
    executionTime: number;
    recommendations: string[];
  }> {
    const connection = await this.acquireConnection();
    try {
      // Get query execution plan
      const explainSql = `EXPLAIN QUERY PLAN ${sql}`;
      const stmt = this.getCachedStatement(explainSql, connection);
      const queryPlan = stmt.all(...params);

      // Measure execution time
      const startTime = performance.now();
      const dataStmt = this.getCachedStatement(sql, connection);
      dataStmt.all(...params);
      const executionTime = performance.now() - startTime;

      // Analyze plan and generate recommendations
      const recommendations: string[] = [];

      for (const step of queryPlan as any[]) {
        const detail = step.detail?.toLowerCase() || '';

        // Check for table scans
        if (detail.includes('scan table')) {
          recommendations.push(`Consider adding an index for table scan: ${detail}`);
        }

        // Check for missing indexes on joins
        if (detail.includes('using automatic covering index')) {
          recommendations.push(`Consider creating a covering index: ${detail}`);
        }

        // Check for inefficient sorting
        if (detail.includes('use temp b-tree for order by')) {
          recommendations.push(`Consider adding an index to avoid sorting: ${detail}`);
        }
      }

      // Performance-based recommendations
      if (executionTime > 10) {
        recommendations.push(`Query execution time (${executionTime.toFixed(2)}ms) is high, consider optimization`);
      }

      return {
        queryPlan,
        executionTime,
        recommendations
      };
    } catch (error) {
      this.logger.error('Failed to analyze query performance:', error);
      throw createStorageError('analyzeQueryPerformance', 'Failed to analyze query performance', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }

  /**
   * Gets database statistics for monitoring and optimization
   */
  async getDatabaseStatistics(): Promise<{
    tableStats: {
      tableName: string;
      rowCount: number;
      pageCount: number;
      avgRowSize: number;
    }[];
    indexStats: {
      indexName: string;
      tableName: string;
      isUnique: boolean;
      columns: string;
    }[];
    pragmaInfo: {
      journalMode: string;
      foreignKeys: boolean;
      cacheSize: number;
      pageSize: number;
    };
  }> {
    const connection = await this.acquireConnection();
    try {

      // Get table statistics
      const tables = this.getCachedStatement("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", connection).all() as {name: string}[];
      const tableStats = [];

      for (const table of tables) {
        try {
          const rowCount = this.getCachedStatement(`SELECT COUNT(*) as count FROM ${table.name}`, connection).get() as {count: number};
          const _pageInfo = this.getCachedStatement(`PRAGMA table_info(${table.name})`, connection).all();
          const pageCount = this.getCachedStatement(`PRAGMA page_count`, connection).get() as {page_count: number};

          tableStats.push({
            tableName: table.name,
            rowCount: rowCount.count,
            pageCount: pageCount.page_count,
            avgRowSize: rowCount.count > 0 ? Math.round((pageCount.page_count * 4096) / rowCount.count) : 0
          });
        } catch (error) {
          this.logger.warn(`Failed to get stats for table ${table.name}:`, error);
        }
      }

      // Get index statistics
      const indexes = this.getCachedStatement("SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'", connection).all() as {
        name: string;
        tbl_name: string;
        sql: string;
      }[];

      const indexStats = indexes.map(index => ({
        indexName: index.name,
        tableName: index.tbl_name,
        isUnique: index.sql?.includes('UNIQUE') || false,
        columns: index.sql?.match(/\((.*?)\)/)?.[1] || 'unknown'
      }));

      // Get pragma information
      const journalMode = connection.db.pragma('journal_mode', {simple: true}) as string;
      const foreignKeys = connection.db.pragma('foreign_keys', {simple: true}) as number;
      const cacheSize = connection.db.pragma('cache_size', {simple: true}) as number;
      const pageSize = connection.db.pragma('page_size', {simple: true}) as number;

      return {
        tableStats,
        indexStats,
        pragmaInfo: {
          journalMode,
          foreignKeys: Boolean(foreignKeys),
          cacheSize,
          pageSize
        }
      };
    } catch (error) {
      this.logger.error('Failed to get database statistics:', error);
      throw createStorageError('getDatabaseStatistics', 'Failed to get database statistics', 'StorageService', error instanceof Error ? error : undefined);
    } finally {
      this.releaseConnection(connection);
    }
  }

  /**
   * Gets connection pool statistics for monitoring and optimization
   */
  async getPoolStatistics(): Promise<PoolStatistics> {
    return {
      ...this.poolStats,
      totalConnections: this.connectionPool.length,
      activeConnections: this.connectionPool.filter(conn => conn.inUse).length,
      idleConnections: this.connectionPool.filter(conn => !conn.inUse).length,
      statementCacheSize: this.statementCache.size
    };
  }

  /**
   * Clears the prepared statement cache
   */
  async clearStatementCache(): Promise<void> {
    this.statementCache.clear();
    this.poolStats.statementCacheSize = 0;
    this.logger.info('Prepared statement cache cleared');
  }

  async dispose(): Promise<void> {
    // Stop cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Clear statement cache
    this.statementCache.clear();

    // Close all connections in the pool
    for (const connection of this.connectionPool) {
      try {
        // Attempt to checkpoint the WAL file to ensure data is written to the main DB file
        this.logger.info(`Attempting WAL checkpoint for connection ${connection.id}...`);
        connection.db.pragma('wal_checkpoint(RESTART)');
        this.logger.info(`WAL checkpoint successful for connection ${connection.id}.`);
      } catch (checkpointError) {
        this.logger.error(`Error during WAL checkpoint for connection ${connection.id}:`, checkpointError);
        // Continue with closing even if checkpoint fails
      }

      try {
        connection.db.close();
        this.logger.info(`Database connection ${connection.id} closed.`);
      } catch (closeError) {
        this.logger.error(`Error closing database connection ${connection.id}:`, closeError);
      }
    }

    // Clear the connection pool
    this.connectionPool = [];
    this.poolStats = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      statementCacheHits: 0,
      statementCacheMisses: 0,
      statementCacheSize: 0,
      averageAcquireTime: 0
    };

    this.logger.info('Database connection pool disposed.');
  }
}

/**
 * Health checker implementations for specific services
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HealthCheckResult, HealthStatus } from './health.service';
import { StorageServiceAPI } from './storage.service';
import { GitServiceAPI } from './git.service';
import { IpcServiceAPI } from './ipc.service';
import { LoggingServiceAPI } from './logging.service';

/**
 * Health checker for Storage Service
 */
export class StorageHealthChecker implements IHealthChecker {
  constructor(private storageService: StorageServiceAPI) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test basic database connectivity
      await this.storageService.get('SELECT 1 as test');
      
      // Test write capability with a simple operation
      const testId = `health-check-${Date.now()}`;
      await this.storageService.run(
        'CREATE TEMP TABLE IF NOT EXISTS health_check (id TEXT PRIMARY KEY, timestamp INTEGER)'
      );
      await this.storageService.run(
        'INSERT OR REPLACE INTO health_check (id, timestamp) VALUES (?, ?)',
        [testId, Date.now()]
      );
      
      // Test read capability
      const result = await this.storageService.get(
        'SELECT * FROM health_check WHERE id = ?',
        [testId]
      );
      
      if (!result) {
        throw new Error('Failed to read test data from database');
      }
      
      return {
        status: HealthStatus.HEALTHY,
        message: 'Database is responsive and functional',
        details: {
          testId,
          readWriteTest: 'passed'
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Database health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

/**
 * Health checker for Git Service
 */
export class GitHealthChecker implements IHealthChecker {
  constructor(private gitService: GitServiceAPI) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test git version to ensure git is available
      // This is a lightweight operation that verifies git functionality
      const testPath = process.cwd(); // Use current working directory for test
      
      // Try to get git status (this will work even if not a git repo)
      try {
        if (this.gitService.getStatus) {
          await this.gitService.getStatus(testPath);
        }
      } catch (error) {
        // If it's not a git repo, that's fine - git is still working
        if (error instanceof Error && error.message.includes('Not a Git repository')) {
          // Git is working, just not a repo
        } else {
          throw error;
        }
      }
      
      return {
        status: HealthStatus.HEALTHY,
        message: 'Git service is functional',
        details: {
          testPath,
          gitAvailable: true
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Git service health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

/**
 * Health checker for IPC Service
 */
export class IpcHealthChecker implements IHealthChecker {
  constructor(private ipcService: IpcServiceAPI) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test IPC service by checking if it can handle a simple operation
      // We'll use a health check channel that should always respond
      const testChannel = 'health:ping';
      const testData = { timestamp: Date.now() };
      
      // Register a temporary handler for health check
      const healthHandler = async (args: any) => {
        return { pong: true, receivedAt: Date.now(), data: args };
      };
      
      this.ipcService.handle(testChannel, healthHandler);
      
      try {
        // Test the IPC call
        const result = await this.ipcService.invoke(testChannel, testData);
        
        if (!(result as any).success || !(result as any).data?.pong) {
          throw new Error('IPC health check did not return expected response');
        }
        
        return {
          status: HealthStatus.HEALTHY,
          message: 'IPC service is responsive',
          details: {
            testChannel,
            roundTripTime: Date.now() - testData.timestamp,
            response: (result as any).data
          },
          timestamp: Date.now(),
          responseTime: Date.now() - startTime,
        };
        
      } finally {
        // Clean up the test handler
        // Note: IpcService doesn't have an unhandle method in the current implementation
        // This is a limitation we might want to address
      }
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `IPC service health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

/**
 * Health checker for Logging Service
 */
export class LoggingHealthChecker implements IHealthChecker {
  constructor(private loggingService: LoggingServiceAPI) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test logging service by creating a scoped logger and logging a test message
      const testLogger = this.loggingService.createScopedLogger('health-check');
      
      // Test different log levels
      testLogger.debug('Health check debug message');
      testLogger.info('Health check info message');
      
      // If we get here without throwing, logging is working
      return {
        status: HealthStatus.HEALTHY,
        message: 'Logging service is functional',
        details: {
          testCompleted: true,
          logLevelsTest: ['debug', 'info']
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Logging service health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

/**
 * Memory usage health checker
 */
export class MemoryHealthChecker implements IHealthChecker {
  constructor(
    private thresholds = {
      warning: 0.8,  // 80% memory usage
      critical: 0.95 // 95% memory usage
    }
  ) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const memoryUsageRatio = usedMemory / totalMemory;
      
      let status: HealthStatus;
      let message: string;
      
      if (memoryUsageRatio >= this.thresholds.critical) {
        status = HealthStatus.CRITICAL;
        message = `Critical memory usage: ${Math.round(memoryUsageRatio * 100)}%`;
      } else if (memoryUsageRatio >= this.thresholds.warning) {
        status = HealthStatus.DEGRADED;
        message = `High memory usage: ${Math.round(memoryUsageRatio * 100)}%`;
      } else {
        status = HealthStatus.HEALTHY;
        message = `Memory usage is normal: ${Math.round(memoryUsageRatio * 100)}%`;
      }
      
      return {
        status,
        message,
        details: {
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          external: memoryUsage.external,
          rss: memoryUsage.rss,
          usageRatio: memoryUsageRatio,
          thresholds: this.thresholds
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `Memory health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

/**
 * File system health checker
 */
export class FileSystemHealthChecker implements IHealthChecker {
  constructor(private testPaths: string[] = [process.cwd()]) {}

  async checkHealth(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const testResults: Record<string, boolean> = {};
      
      for (const testPath of this.testPaths) {
        try {
          // Test read access
          await fs.access(testPath, (await import('fs')).constants.R_OK);
          
          // Test write access by creating a temporary file
          const tempFile = path.join(testPath, `.health-check-${Date.now()}.tmp`);
          await fs.writeFile(tempFile, 'health check');
          await fs.unlink(tempFile);
          
          testResults[testPath] = true;
        } catch (_error) {
          testResults[testPath] = false;
        }
      }
      
      const failedPaths = Object.entries(testResults)
        .filter(([, success]) => !success)
        .map(([path]) => path);
      
      if (failedPaths.length === 0) {
        return {
          status: HealthStatus.HEALTHY,
          message: 'File system access is working',
          details: {
            testedPaths: this.testPaths,
            results: testResults
          },
          timestamp: Date.now(),
          responseTime: Date.now() - startTime,
        };
      } else {
        return {
          status: failedPaths.length === this.testPaths.length ? HealthStatus.UNHEALTHY : HealthStatus.DEGRADED,
          message: `File system access failed for ${failedPaths.length} path(s)`,
          details: {
            testedPaths: this.testPaths,
            results: testResults,
            failedPaths
          },
          timestamp: Date.now(),
          responseTime: Date.now() - startTime,
        };
      }
      
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        message: `File system health check failed: ${error instanceof Error ? error.message : String(error)}`,
        details: {
          error: error instanceof Error ? error.message : String(error)
        },
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }
}

import ElectronStore from 'electron-store';
import fs from 'node:fs'; // Added fs import
import {Emitter} from '@shared/utils/emitter';
import type {AppSettings, SettingDeclaration} from '@shared/types/settings';
import type {ConfigurationInspect, IConfigurationService} from '@shared/types/configuration';
import type {Disposable, JsonValue} from '@shared/types/common';
import type {LoggingServiceAPI} from './logging.service';
import type {IpcServiceAPI} from './ipc.service';
import {BaseService, IBaseService} from './base.service';
import {defaultProjectsRoot, defaults, schema} from '../defaults/settings'; // Reuse existing defaults/schema and add defaultProjectsRoot
import {IpcChannels} from '@shared/constants/ipc-channels';
import {BrowserWindow} from 'electron';

// --- API Interface (Extending BaseService) ---
export interface ConfigurationServiceAPI extends IConfigurationService, IBaseService {
  registerSetting(declaration: SettingDeclaration): Disposable;

  getAllSettingsDeclarations(): SettingDeclaration[]; // Keep for Settings UI for now
  getAll(): Record<string, JsonValue>; // Get all configuration values
}

export class ConfigurationService extends BaseService implements ConfigurationServiceAPI {
  private store: ElectronStore<AppSettings>;
  private declarations = new Map<string, SettingDeclaration>();
  private readonly ipcService: IpcServiceAPI;
  
  private readonly _onDidChangeConfiguration = new Emitter<{ key: string; newValue: JsonValue }>();
  readonly onDidChangeConfiguration = this._onDidChangeConfiguration.event;
  
  constructor(logger: LoggingServiceAPI, ipcService: IpcServiceAPI) {
    try {
      super(logger, 'ConfigurationService');
      // Define schema and defaults for electron-store
      this.store = new ElectronStore<AppSettings>({schema, defaults});
      this.ipcService = ipcService;
      this.logger.info('Initialized electron-store with schema and defaults.');
      // Directory check will happen in initialize
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : String(error);
      logger.error('Failed to initialize ConfigurationService', error);
      throw new Error(`Failed to initialize ConfigurationService: ${message}`);
    }
  }
  
  public async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    await super.initialize(windowGetter);
    this.registerCoreSettings(); // Register core settings
    await this.ensureDefaultProjectsRootExists(); // Ensure directory exists after registration
    this.registerIpcHandlers();
    this.isInitialized = true;
    this.logger.info('ConfigurationService initialized.');
  }
  
  // --- Setting Declaration Management ---
  registerSetting(declaration: SettingDeclaration): Disposable {
    if (!declaration || !declaration.id) {
      this.logger.error('Attempted to register setting without an ID.');
      return {
        dispose: async () => { /* no op */
        }
      };
    }
    if (this.declarations.has(declaration.id)) {
      this.logger.warn(`Setting declaration '${declaration.id}' is already registered. Overwriting.`);
    }
    this.logger.info(`Registering setting declaration: '${declaration.id}'`);
    this.declarations.set(declaration.id, declaration);
    return {
      dispose: async () => {
        this.logger.info(`Disposing setting declaration: '${declaration.id}'`);
        this.declarations.delete(declaration.id);
        // TODO: Should we notify about declaration removal?
      }
    };
  }
  
  getAllSettingsDeclarations(): SettingDeclaration[] {
    return Array.from(this.declarations.values());
  }
  
  async getValue<T extends JsonValue>(key: string, providedDefaultValue?: T): Promise<T> {
    // TODO: Implement scope handling (User > Workspace > Default) later
    // For now, just User > Default
    
    // 1. Check electron-store (User settings)
    const storedValue = this.store.get(key);
    if (storedValue !== undefined) {
      // TODO: Add type validation against declaration?
      return storedValue as T;
    }
    
    // 2. Check registered declaration default
    const declaration = this.declarations.get(key);
    if (declaration && declaration.default !== undefined) {
      return declaration.default as T;
    }
    
    // 3. Use provided default value
    if (providedDefaultValue !== undefined) {
      return providedDefaultValue;
    }
    
    // 4. Return undefined (or throw?) if not found anywhere
    this.logger.warn(`Configuration key '${key}' not found in store or declarations, and no default provided.`);
    // Returning undefined might be problematic for non-nullable types.
    // Consider throwing or requiring a default in the method signature.
    // Wrap synchronous return in Promise.resolve()
    return Promise.resolve(undefined as unknown as T);
  }
  
  async updateValue(key: string, value: JsonValue): Promise<void> {
    // TODO: Implement scope handling (only User scope for now)
    const declaration = this.declarations.get(key);
    
    // --- Validation (Simplified from SettingsService) ---
    if (declaration) {
      // Basic type check
      const expectedType = declaration.type;
      const actualType = typeof value;
      let isValid = false;
      // Add more robust validation later (min/max, enum etc.)
      switch (expectedType) {
        case 'string':
          isValid = actualType === 'string';
          break;
        case 'number':
          isValid = actualType === 'number' && !isNaN(value as number);
          break;
        case 'boolean':
          isValid = actualType === 'boolean';
          break;
        case 'array':
          isValid = Array.isArray(value);
          break;
        case 'object':
          isValid = actualType === 'object' && value !== null && !Array.isArray(value);
          break;
        default:
          isValid = true; // Allow unknown types for now
      }
      if (!isValid) {
        const errorMsg = `Type validation failed for setting '${key}'. Expected ${expectedType}, got ${actualType}.`;
        this.logger.error(errorMsg);
        throw new Error(errorMsg); // Throw error on invalid update
      }
    } else {
      // Allow setting undeclared keys for flexibility? Or restrict?
      // VS Code allows this but flags them in settings.json.
      this.logger.warn(`Updating value for undeclared configuration key '${key}'.`);
    }
    
    // --- Persistence & Notification ---
    try {
      const oldValue = this.store.get(key);
      const oldValueJson = JSON.stringify(oldValue);
      const newValueJson = JSON.stringify(value);
      
      if (oldValueJson !== newValueJson) {
        this.logger.info(`Updating configuration key '${key}' from ${oldValueJson} to ${newValueJson}`);
        // Check if value is the default - if so, remove from store
        if (declaration && newValueJson === JSON.stringify(declaration.default)) {
          this.store.delete(key as keyof AppSettings);
          this.logger.info(`Configuration key '${key}' reset to default value.`);
        } else {
          this.store.set(key, value);
        }
        
        // Notify listeners (internal and via IPC)
        const effectiveValue = await this.getValue(key); // Await the promise
        const changeEvent = {key, newValue: effectiveValue}; // Use the resolved value
        this._onDidChangeConfiguration.fire(changeEvent);
        this.ipcService.send(IpcChannels.CONFIGURATION_DID_CHANGE, changeEvent); // Use correct channel
        
      } else {
        this.logger.info(`Configuration key '${key}' value unchanged. Skipping update.`);
      }
    } catch (error: unknown) {
      this.logger.error(`Failed to update configuration key '${key}'`, error);
      throw new Error(`Failed to update configuration key '${key}': ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  // --- IConfigurationService Implementation ---
  
  async inspect<T extends JsonValue>(key: string): Promise<ConfigurationInspect<T>> {
    // TODO: Implement scope handling later
    const declaration = this.declarations.get(key);
    const userValue = this.store.get(key) as T | undefined;
    // const effectiveValue = this.getValue(key) as T; // Calculate effective value based on scopes

    // Return the object wrapped in a resolved promise
    return Promise.resolve({
      key,
      defaultValue: declaration?.default as T | undefined,
      userValue: userValue,
      // workspaceValue: undefined, // Placeholder
      // effectiveValue: effectiveValue, // Placeholder
    });
  }

  getAll(): Record<string, JsonValue> {
    // Return all stored configuration values
    const allValues: Record<string, JsonValue> = {};

    // Get all values from the store
    for (const [key, value] of Object.entries(this.store.store)) {
      allValues[key] = value;
    }

    return allValues;
  }
  
  // --- Cleanup ---
  public async dispose() {
    this.logger.info('Disposing ConfigurationService...');
    
    // Remove IPC handlers
    try {
      this.ipcService.removeHandler(IpcChannels.CONFIGURATION_GET_VALUE);
      this.ipcService.removeHandler(IpcChannels.CONFIGURATION_UPDATE_VALUE);
      this.ipcService.removeHandler(IpcChannels.CONFIGURATION_INSPECT);
      this.ipcService.removeHandler(IpcChannels.SETTINGS_GET_DECLARATIONS);
    } catch (error) {
      this.logger.warn('Error removing IPC handlers during disposal:', error);
    }
    
    // Dispose event emitter
    this._onDidChangeConfiguration.dispose();
    
    // Clear declarations
    this.declarations.clear();
    
    // Call parent dispose
    await super.dispose();
    this.logger.info('ConfigurationService disposed successfully');
  }
  
  /**
   * Registers core application settings declarations.
   * Moved from SettingsService.
   */
  private registerCoreSettings(): void {
    this.logger.info('Registering core settings declarations...');
    
    // Register workbench.projectsRoot first
    this.registerSetting({
      id: 'workbench.projectsRoot',
      label: 'Projects Root Directory',
      description: 'The default root directory where new book projects will be created.',
      type: 'string',
      format: 'path',
      scope: 'user',
      default: defaultProjectsRoot
    });
    
    // Note: ensureDefaultProjectsRootExists is called after this in initialize()
    
    this.registerSetting({
      id: 'workbench.theme',
      label: 'Color Theme',
      description: 'Specifies the color theme used in the workbench.',
      type: 'string', // Тип данных - строка (ID темы или 'system')
      controlType: 'themeSelect', // Используем специальный тип контрола
      scope: 'user',
      default: 'system'
    });
    
    this.registerSetting({
      id: 'editor.fontSize',
      label: 'Font Size', // Добавляем label
      description: 'Controls the font size in pixels.',
      type: 'number',
      scope: 'user',
      default: defaults.editorFontSize,
      minimum: 10,
      maximum: 30
    });
    
    this.registerSetting({
      id: 'editor.lineHeight',
      label: 'Line Height', // Добавляем label
      description: 'Controls the line height. Use 0 to compute based on font size.',
      type: 'number',
      scope: 'user',
      default: defaults.editorLineHeight,
      minimum: 1.0,
      maximum: 3.0
    });
    
    this.registerSetting({
      id: 'files.autosaveInterval', // Consider renaming to books.autosaveInterval?
      label: 'Autosave Interval', // Добавляем label
      description: 'Interval in milliseconds for automatic saving. Set to 0 to disable.',
      type: 'number',
      scope: 'user',
      default: defaults.autosaveInterval,
      minimum: 0 // 0 для отключения
    });
    
    this.logger.info('Core settings declarations registered.');
  }
  
  /**
   * Ensures the default directory for projects exists.
   * Moved from SettingsService.
   */
  private async ensureDefaultProjectsRootExists(): Promise<void> {
    try {
      // Use this service's getValue method
      const projectsRoot = await this.getValue<string>('workbench.projectsRoot', defaultProjectsRoot);
      if (!fs.existsSync(projectsRoot)) {
        this.logger.info(`Default projects root directory "${projectsRoot}" does not exist. Creating...`);
        fs.mkdirSync(projectsRoot, {recursive: true});
      }
    } catch (error) {
      this.logger.error('Failed to ensure default projects root directory exists:', error);
    }
  }
  
  // --- IPC Handlers ---
  private registerIpcHandlers(): void {
    this.logger.info('Registering ConfigurationService IPC handlers...');
    
    // Handler for renderer requesting a value
    this.ipcService.handle<{ key: string; defaultValue?: JsonValue }, JsonValue>(
      IpcChannels.CONFIGURATION_GET_VALUE, // Use correct channel
      async (args) => {
        if (!args) {
          throw new Error('Invalid arguments for CONFIGURATION_GET_VALUE');
        }
        // Use 'this' to access service methods
        return this.getValue(args.key, args.defaultValue);
      }
    );
    
    // Handler for renderer requesting an update
    // Input type is { key: string; value: JsonValue }, output is Promise<void>
    this.ipcService.handle<{ key: string; value: JsonValue }, Promise<void>>(
      IpcChannels.CONFIGURATION_UPDATE_VALUE, // Use correct channel
      async (args) => {
        if (!args) {
          throw new Error('Invalid arguments for CONFIGURATION_UPDATE_VALUE');
        }
        // Use 'this' to access service methods
        await this.updateValue(args.key, args.value);
      }
    );
    
    // Handler for renderer requesting inspection (optional)
    this.ipcService.handle<{ key: string }, ConfigurationInspect<JsonValue>>(
      IpcChannels.CONFIGURATION_INSPECT, // Use correct channel
      async (args) => {
        if (!args) {
          throw new Error('Invalid arguments for CONFIGURATION_INSPECT');
        }
        return this.inspect(args.key);
      }
    );
    
    // Handler to get all declarations (still needed for Settings UI)
    // Input type is undefined/null, output is { declarations: SettingDeclaration[] }
    this.ipcService.handle<undefined | null, { declarations: SettingDeclaration[] }>(
      IpcChannels.SETTINGS_GET_DECLARATIONS, // Use correct channel
      async () => { // No args expected
        return {declarations: this.getAllSettingsDeclarations()};
      }
    );
    
    
    this.logger.info('ConfigurationService IPC handlers registered.');
  }
}

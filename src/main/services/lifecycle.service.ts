import {
  AppLifecycle,
  AppLifecycleEvent,
  AppLifecycleEventData,
  AppLifecycleEventHandler
} from '@shared/types/lifecycle-events';
import {Disposable} from '@shared/types/common';
import {LoggingServiceAPI} from './logging.service';
import {BaseService} from './base.service';

/**
 * Реализация сервиса управления жизненным циклом приложения.
 */
export class LifecycleService extends BaseService implements AppLifecycle { // Renamed IAppLifecycle
  private eventHandlers = new Map<AppLifecycleEvent, Set<AppLifecycleEventHandler>>();
  private eventHistory = new Map<AppLifecycleEvent, AppLifecycleEventData>();
  private pendingEmits: Promise<void> = Promise.resolve();
  
  constructor(logger: LoggingServiceAPI) {
    super(logger, 'AppLifecycleService');
  }
  
  /**
   * Подписывается на событие жизненного цикла приложения.
   * @param event Событие, на которое нужно подписаться
   * @param handler Функция обработчик события
   * @returns Объект для отписки от события
   */
  public on(event: AppLifecycleEvent, handler: AppLifecycleEventHandler): Disposable { // Renamed Disposable
    this.throwIfDisposed();
    
    // Получаем или создаем набор обработчиков для события
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    
    let handlers = this.eventHandlers.get(event);
    if (!handlers) {
      handlers = new Set();
      this.eventHandlers.set(event, handlers);
    }
    handlers.add(handler);
    
    // Если событие уже произошло, немедленно вызываем обработчик
    if (this.eventHistory.has(event)) {
      const eventData = this.eventHistory.get(event);
      if (eventData !== undefined) {
        try {
          handler(eventData);
        } catch (error) {
          this.logger.error(`Error in event handler for ${event}:`, error);
        }
      }
    }
    
    // Возвращаем объект для отписки
    return {
      dispose: async () => {
        if (this.eventHandlers.has(event)) {
          const handlersSet = this.eventHandlers.get(event);
          if (handlersSet) {
            handlersSet.delete(handler);
            
            if (handlersSet.size === 0) {
              this.eventHandlers.delete(event);
            }
          }
        }
      }
    };
  }
  
  /**
   * Генерирует событие жизненного цикла приложения.
   * @param event Событие для генерации
   * @param details Дополнительные данные события
   */
  public async emit(event: AppLifecycleEvent, details?: Record<string, unknown>): Promise<void> {
    this.throwIfDisposed();
    
    // Создаем данные события
    const eventData: AppLifecycleEventData = {
      timestamp: Date.now(),
      details
    };
    
    // Сохраняем событие в истории
    this.eventHistory.set(event, eventData);
    this.logger.info(`Emitting lifecycle event: ${event}`, details);
    
    // Получаем обработчики
    const handlers = this.eventHandlers.get(event);
    
    if (!handlers || handlers.size === 0) {
      return;
    }
    
    // Выполняем обработчики последовательно, но не блокируем эмитента
    this.pendingEmits = this.pendingEmits.then(async () => {
      for (const handler of handlers) {
        try {
          await handler(eventData);
        } catch (error) {
          this.logger.error(`Error in event handler for ${event}:`, error);
        }
      }
    });
    
    // Возвращаем Promise, который разрешится, когда все обработчики будут вызваны
    return this.pendingEmits;
  }
  
  /**
   * Возвращает текущее состояние жизненного цикла.
   */
  public getCurrentState(): Map<AppLifecycleEvent, AppLifecycleEventData> {
    this.throwIfDisposed();
    return new Map(this.eventHistory);
  }
  
  /**
   * Проверяет, произошло ли указанное событие.
   * @param event Событие для проверки
   */
  public hasEventOccurred(event: AppLifecycleEvent): boolean {
    this.throwIfDisposed();
    return this.eventHistory.has(event);
  }
  
  /**
   * Освобождает ресурсы сервиса.
   */
  public async dispose(): Promise<void> {
    this.logger.info('Disposing LifecycleService...');

    // Clear all event handlers
    this.eventHandlers.clear();

    // Clear event history
    this.eventHistory.clear();

    // Call parent dispose
    await super.dispose();
    this.logger.info('LifecycleService disposed successfully');
  }
}

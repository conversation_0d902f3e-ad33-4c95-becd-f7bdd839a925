import {BrowserWindow, ipcMain} from 'electron';
import {IpcErrorData, IpcInvokeArgs, IpcInvokeResult} from '@shared/types/ipc';
import {LoggingServiceAPI} from './logging.service';
import {BaseService, IBaseService} from "./base.service";
import {Disposable} from "@shared/types/common";
import {IpcChannels} from '@shared/constants/ipc-channels';
import {errorToIpcError} from '@shared/errors';

/**
 * Интерфейс API для IpcService
 */
export interface IpcServiceAPI extends IBaseService {
  handle<TArgs = unknown, TResult = unknown>(
    channel: string,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): Disposable;
  
  send<TData = unknown>(
    channel: string,
    data: TData
  ): void;
  
  removeHandler(channel: string): void;
  
  notifyShutdown(): void;
}

/**
 * Сервис для управления IPC коммуникацией между главным и рендер процессами
 */
export class IpcService extends BaseService implements IpcServiceAPI {
  private _isShuttingDown = false;
  
  constructor(logger: LoggingServiceAPI) {
    super(logger, 'IPC Main');
  }
  
  /**
   * Инициализирует сервис с функцией для получения текущего окна
   */
  public async initialize(getCurrentWindowFunc: () => (BrowserWindow | null)) {
    await super.initialize(getCurrentWindowFunc);
    this.isInitialized = true;
  }
  
  /**
   * Регистрирует обработчик IPC для канала
   */
  handle<TArgs = unknown, TResult = unknown>(
    channel: string,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): Disposable {
    this.throwIfDisposed();
    
    ipcMain.handle(
      channel,
      async (_event, invokeArgs: IpcInvokeArgs<TArgs>): Promise<IpcInvokeResult<TResult>> => {
        const {payload} = invokeArgs;
        if (channel !== IpcChannels.LOG_MESSAGE) this.logger.info(`Request <- ${channel}`, {payload});
        try {
          const result = await handler(payload);
          if (channel !== IpcChannels.LOG_MESSAGE) this.logger.info(`Response -> ${channel}`, {result});
          return {success: true, data: result};
        } catch (error: unknown) {
          const ipcError: IpcErrorData = errorToIpcError(error, `IPC:${channel}`);
          this.logger.error(`[IPC Handle Error] -> ${channel}`, JSON.stringify({error: ipcError}));
          return {success: false, error: ipcError};
        }
      }
    );
    
    this.logger.info(`Registered handler for channel: ${channel}`);
    
    return {
      dispose: async () => this.removeHandler(channel)
    };
  }
  
  /**
   * Отправляет событие/сообщение в текущий процесс рендерера
   */
  public send<TData = unknown>(channel: string, data: TData): void {
    this.throwIfDisposed();
    
    if (!this.windowGetter) {
      this.logger.warn(`[IPC Send Event] Attempted to send on channel ${channel} before IpcService was initialized.`);
      return;
    }
    
    if (this._isShuttingDown) {
      return;
    }
    
    const targetWebContents = this.windowGetter()?.webContents;
    if (targetWebContents && !targetWebContents.isDestroyed()) {
      this.logger.info(`[IPC Send Event] -> ${channel}`, {payload: data});
      targetWebContents.send(channel, {payload: data});
    } else {
      this.logger.warn(
        `[IPC Send Event] No active or valid window found to send on channel: ${channel}`
      );
    }
  }
  
  public sendToRenderer(channel: string, data: unknown): void {
    this.send(channel, data);
  }
  
  /**
   * Удаляет обработчик IPC
   */
  removeHandler(channel: string): void {
    this.throwIfDisposed();
    ipcMain.removeHandler(channel);
    this.logger.info(`Removed handler for channel: ${channel}`);
  }
  
  /**
   * Уведомляет сервис о том, что приложение завершает работу
   */
  notifyShutdown(): void {
    this.logger.info('Shutdown notified. IPC sending will be disabled.');
    this._isShuttingDown = true;
  }
  
  /**
   * Освобождает ресурсы, используемые сервисом
   */
  public async dispose() {
    await super.dispose();
    this.notifyShutdown();
  }
  
  // Removed formatError method - now using standardized errorToIpcError from shared/errors
}

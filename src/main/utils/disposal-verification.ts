import { LoggingServiceAPI } from '../services/logging.service';
import { IBaseService } from '../services/base.service';

/**
 * Resource types that should be cleaned up during disposal
 */
export enum ResourceType {
  TIMER = 'timer',
  INTERVAL = 'interval',
  EVENT_LISTENER = 'event_listener',
  IPC_HANDLER = 'ipc_handler',
  DATABASE_CONNECTION = 'database_connection',
  FILE_HANDLE = 'file_handle',
  NETWORK_CONNECTION = 'network_connection',
  CHILD_PROCESS = 'child_process',
  MEMORY_ALLOCATION = 'memory_allocation',
  SUBSCRIPTION = 'subscription'
}

/**
 * Resource cleanup verification result
 */
export interface ResourceCleanupResult {
  resourceType: ResourceType;
  isCleanedUp: boolean;
  details: string;
  recommendation?: string;
}

/**
 * Service disposal verification result
 */
export interface DisposalVerificationResult {
  serviceName: string;
  isProperlyDisposed: boolean;
  resourceCleanupResults: ResourceCleanupResult[];
  overallScore: number; // 0-100
  criticalIssues: string[];
  warnings: string[];
  recommendations: string[];
}

/**
 * Utility class for verifying proper resource cleanup and disposal patterns
 */
export class DisposalVerificationUtility {
  private logger: LoggingServiceAPI;

  constructor(logger: LoggingServiceAPI) {
    this.logger = logger;
  }

  /**
   * Verify that a service properly implements disposal patterns
   */
  async verifyServiceDisposal(service: IBaseService, serviceName: string): Promise<DisposalVerificationResult> {
    this.logger.info(`Verifying disposal patterns for service: ${serviceName}`);

    const resourceCleanupResults: ResourceCleanupResult[] = [];
    const criticalIssues: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // Check if service implements dispose method
    if (typeof service.dispose !== 'function') {
      criticalIssues.push('Service does not implement dispose() method');
      return {
        serviceName,
        isProperlyDisposed: false,
        resourceCleanupResults,
        overallScore: 0,
        criticalIssues,
        warnings,
        recommendations: ['Implement dispose() method that extends BaseService.dispose()']
      };
    }

    // Verify disposal implementation
    await this.verifyDisposalImplementation(service, serviceName, resourceCleanupResults, criticalIssues, warnings, recommendations);

    // Calculate overall score
    const totalChecks = resourceCleanupResults.length;
    const passedChecks = resourceCleanupResults.filter(r => r.isCleanedUp).length;
    const overallScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 0;

    const isProperlyDisposed = criticalIssues.length === 0 && overallScore >= 80;

    return {
      serviceName,
      isProperlyDisposed,
      resourceCleanupResults,
      overallScore,
      criticalIssues,
      warnings,
      recommendations
    };
  }

  /**
   * Verify disposal implementation details
   */
  private async verifyDisposalImplementation(
    service: IBaseService,
    serviceName: string,
    resourceCleanupResults: ResourceCleanupResult[],
    criticalIssues: string[],
    warnings: string[],
    recommendations: string[]
  ): Promise<void> {
    
    // Check for common resource cleanup patterns
    const serviceCode = service.constructor.toString();
    const serviceInstance = service as any;

    // Check for timer cleanup
    this.checkTimerCleanup(serviceInstance, serviceCode, resourceCleanupResults, warnings);

    // Check for event listener cleanup
    this.checkEventListenerCleanup(serviceInstance, serviceCode, resourceCleanupResults, warnings);

    // Check for IPC handler cleanup
    this.checkIpcHandlerCleanup(serviceInstance, serviceCode, resourceCleanupResults, warnings);

    // Check for database connection cleanup
    this.checkDatabaseConnectionCleanup(serviceInstance, serviceCode, resourceCleanupResults, warnings);

    // Check for subscription cleanup
    this.checkSubscriptionCleanup(serviceInstance, serviceCode, resourceCleanupResults, warnings);

    // Check if dispose calls super.dispose()
    this.checkSuperDisposeCall(serviceCode, criticalIssues, recommendations);

    // Check for proper error handling in dispose
    this.checkDisposeErrorHandling(serviceCode, warnings, recommendations);
  }

  /**
   * Check for proper timer cleanup
   */
  private checkTimerCleanup(
    serviceInstance: any,
    serviceCode: string,
    resourceCleanupResults: ResourceCleanupResult[],
    warnings: string[]
  ): void {
    const hasTimers = serviceCode.includes('setTimeout') || serviceCode.includes('setInterval');
    const hasClearTimers = serviceCode.includes('clearTimeout') || serviceCode.includes('clearInterval');

    if (hasTimers) {
      const isCleanedUp = hasClearTimers;
      resourceCleanupResults.push({
        resourceType: ResourceType.TIMER,
        isCleanedUp,
        details: isCleanedUp ? 'Timers are properly cleared' : 'Timers may not be properly cleared',
        recommendation: isCleanedUp ? undefined : 'Add clearTimeout/clearInterval calls in dispose method'
      });

      if (!isCleanedUp) {
        warnings.push('Service uses timers but may not clear them in dispose()');
      }
    }
  }

  /**
   * Check for proper event listener cleanup
   */
  private checkEventListenerCleanup(
    serviceInstance: any,
    serviceCode: string,
    resourceCleanupResults: ResourceCleanupResult[],
    warnings: string[]
  ): void {
    const hasEventListeners = serviceCode.includes('addEventListener') || serviceCode.includes('.on(');
    const hasRemoveListeners = serviceCode.includes('removeEventListener') || serviceCode.includes('.off(') || serviceCode.includes('.removeListener');

    if (hasEventListeners) {
      const isCleanedUp = hasRemoveListeners;
      resourceCleanupResults.push({
        resourceType: ResourceType.EVENT_LISTENER,
        isCleanedUp,
        details: isCleanedUp ? 'Event listeners are properly removed' : 'Event listeners may not be properly removed',
        recommendation: isCleanedUp ? undefined : 'Add event listener removal in dispose method'
      });

      if (!isCleanedUp) {
        warnings.push('Service uses event listeners but may not remove them in dispose()');
      }
    }
  }

  /**
   * Check for proper IPC handler cleanup
   */
  private checkIpcHandlerCleanup(
    serviceInstance: any,
    serviceCode: string,
    resourceCleanupResults: ResourceCleanupResult[],
    warnings: string[]
  ): void {
    const hasIpcHandlers = serviceCode.includes('ipcService.handle') || serviceCode.includes('ipcMain.handle');
    const hasRemoveHandlers = serviceCode.includes('removeHandler') || serviceCode.includes('ipcMain.removeHandler');

    if (hasIpcHandlers) {
      const isCleanedUp = hasRemoveHandlers;
      resourceCleanupResults.push({
        resourceType: ResourceType.IPC_HANDLER,
        isCleanedUp,
        details: isCleanedUp ? 'IPC handlers are properly removed' : 'IPC handlers may not be properly removed',
        recommendation: isCleanedUp ? undefined : 'Add IPC handler removal in dispose method'
      });

      if (!isCleanedUp) {
        warnings.push('Service registers IPC handlers but may not remove them in dispose()');
      }
    }
  }

  /**
   * Check for proper database connection cleanup
   */
  private checkDatabaseConnectionCleanup(
    serviceInstance: any,
    serviceCode: string,
    resourceCleanupResults: ResourceCleanupResult[],
    warnings: string[]
  ): void {
    const hasDbConnections = serviceCode.includes('Database') || serviceCode.includes('.db') || serviceInstance.db;
    const hasDbClose = serviceCode.includes('.close()') || serviceCode.includes('dispose()');

    if (hasDbConnections) {
      const isCleanedUp = hasDbClose;
      resourceCleanupResults.push({
        resourceType: ResourceType.DATABASE_CONNECTION,
        isCleanedUp,
        details: isCleanedUp ? 'Database connections are properly closed' : 'Database connections may not be properly closed',
        recommendation: isCleanedUp ? undefined : 'Add database connection closure in dispose method'
      });

      if (!isCleanedUp) {
        warnings.push('Service uses database connections but may not close them in dispose()');
      }
    }
  }

  /**
   * Check for proper subscription cleanup
   */
  private checkSubscriptionCleanup(
    serviceInstance: any,
    serviceCode: string,
    resourceCleanupResults: ResourceCleanupResult[],
    warnings: string[]
  ): void {
    const hasSubscriptions = serviceCode.includes('subscribe') || serviceCode.includes('subscriptions');
    const hasUnsubscribe = serviceCode.includes('unsubscribe') || serviceCode.includes('.dispose()') || serviceCode.includes('.clear()');

    if (hasSubscriptions) {
      const isCleanedUp = hasUnsubscribe;
      resourceCleanupResults.push({
        resourceType: ResourceType.SUBSCRIPTION,
        isCleanedUp,
        details: isCleanedUp ? 'Subscriptions are properly disposed' : 'Subscriptions may not be properly disposed',
        recommendation: isCleanedUp ? undefined : 'Add subscription disposal in dispose method'
      });

      if (!isCleanedUp) {
        warnings.push('Service has subscriptions but may not dispose them properly');
      }
    }
  }

  /**
   * Check if dispose method calls super.dispose()
   */
  private checkSuperDisposeCall(
    serviceCode: string,
    criticalIssues: string[],
    recommendations: string[]
  ): void {
    const disposeMethod = this.extractDisposeMethod(serviceCode);
    if (disposeMethod && !disposeMethod.includes('super.dispose()')) {
      // This is a warning, not a critical issue for most services
      recommendations.push('Consider adding await super.dispose() call at the end of dispose method');
    }
  }

  /**
   * Check for proper error handling in dispose method
   */
  private checkDisposeErrorHandling(
    serviceCode: string,
    warnings: string[],
    recommendations: string[]
  ): void {
    const disposeMethod = this.extractDisposeMethod(serviceCode);
    if (disposeMethod && !disposeMethod.includes('try') && !disposeMethod.includes('catch')) {
      warnings.push('dispose() method lacks error handling');
      recommendations.push('Add try-catch blocks in dispose method to handle cleanup errors gracefully');
    }
  }

  /**
   * Extract dispose method from service code
   */
  private extractDisposeMethod(serviceCode: string): string | null {
    const disposeMatch = serviceCode.match(/dispose\s*\([^)]*\)\s*{[^}]*}/);
    return disposeMatch ? disposeMatch[0] : null;
  }

  /**
   * Generate a comprehensive disposal verification report
   */
  generateReport(results: DisposalVerificationResult[]): string {
    let report = '# Service Disposal Verification Report\n\n';
    
    const totalServices = results.length;
    const properlyDisposed = results.filter(r => r.isProperlyDisposed).length;
    const averageScore = results.reduce((sum, r) => sum + r.overallScore, 0) / totalServices;

    report += `## Summary\n`;
    report += `- Total Services: ${totalServices}\n`;
    report += `- Properly Disposed: ${properlyDisposed} (${Math.round((properlyDisposed / totalServices) * 100)}%)\n`;
    report += `- Average Score: ${averageScore.toFixed(1)}/100\n\n`;

    for (const result of results) {
      report += `## ${result.serviceName}\n`;
      report += `- Status: ${result.isProperlyDisposed ? '✅ PASS' : '❌ FAIL'}\n`;
      report += `- Score: ${result.overallScore}/100\n`;

      if (result.criticalIssues.length > 0) {
        report += `- Critical Issues:\n`;
        result.criticalIssues.forEach(issue => report += `  - ❌ ${issue}\n`);
      }

      if (result.warnings.length > 0) {
        report += `- Warnings:\n`;
        result.warnings.forEach(warning => report += `  - ⚠️ ${warning}\n`);
      }

      if (result.recommendations.length > 0) {
        report += `- Recommendations:\n`;
        result.recommendations.forEach(rec => report += `  - 💡 ${rec}\n`);
      }

      report += '\n';
    }

    return report;
  }
}

import {<PERSON>rowserWindow} from 'electron';
import {IpcServiceAPI} from '@services/ipc.service';
import {LoggingService} from '@services/logging.service';
import {Disposable} from '@shared/types/common';

/**
 * Provides an extension-specific IPC handling mechanism that automatically
 * prefixes channel names with the extension's ID.
 * Implements the IpcServiceAPI interface.
 */
export class ExtensionIpcService implements IpcServiceAPI {
  private readonly extensionId: string;
  private readonly logger: LoggingService;
  private readonly ipcMain: IpcServiceAPI; // Store the main IpcService instance
  
  constructor(logger: LoggingService, ipcMain: IpcServiceAPI, extensionId: string) {
    this.extensionId = extensionId;
    this.ipcMain = ipcMain; // Use the passed main IpcService instance
    // Create a scoped logger for this specific extension's IPC service
    this.logger = logger.createScopedLogger(`IPC [${extensionId}]`);
  }
  
  public async initialize(_getCurrentWindowFunc: () => (BrowserWindow | null)) {
    this.logger.info('Initialized (delegating to main IpcService).');
  }
  
  /**
   * Registers an IPC handler for an invoke/handle channel specific to this extension.
   * @param channel The channel name within the extension (e.g., 'getBooks').
   * @param handler The function to handle the request.
   */
  handle<TArgs = unknown, TResult = unknown>(
    channel: string,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): Disposable { // Return Disposable
    const prefixedChannel = this.prefixChannel(channel);
    this.logger.info(`Registering handler for channel: ${prefixedChannel} (original: ${channel})`);
    // Delegate to the global this.ipcMain and return its Disposable
    return this.ipcMain.handle(prefixedChannel, handler);
  }
  
  /**
   * Removes an IPC handler specific to this extension.
   * @param channel The channel name within the extension.
   */
  removeHandler(channel: string): void {
    const prefixedChannel = this.prefixChannel(channel);
    this.logger.info(`Removing handler for channel: ${prefixedChannel} (original: ${channel})`);
    // Delegate to the global this.ipcMain
    this.ipcMain.removeHandler(prefixedChannel);
  }
  
  // Update send method to remove targetWindow parameter
  send<TData = unknown>(
    channel: string,
    data: TData
  ): void {
    const prefixedChannel = this.prefixChannel(channel);
    this.logger.info(`Sending event to channel: ${prefixedChannel} (original: ${channel})`, {data});
    // Delegate to the global ipcMain, which now handles getting the target window
    this.ipcMain.send(prefixedChannel, data);
  }
  
  async dispose() {
    this.logger.info(`Disposing IPC service for extension: ${this.extensionId}`);
  }
  
  // Remove formatError as it's handled by the main IpcService
  
  notifyShutdown(): void {
    this.logger.info(`Notifying shutdown for extension: ${this.extensionId}`);
    this.ipcMain.notifyShutdown();
  }

  // Additional methods required by IpcServiceAPI
  async invoke<TResult = unknown, TArgs = unknown>(
    channel: string,
    args: TArgs
  ): Promise<TResult> {
    const prefixedChannel = this.prefixChannel(channel);
    return this.ipcMain.invoke(prefixedChannel, args);
  }

  sendToRenderer<TData = unknown>(
    channel: string,
    data: TData
  ): void {
    const prefixedChannel = this.prefixChannel(channel);
    this.ipcMain.sendToRenderer(prefixedChannel, data);
  }

  on<TData = unknown>(
    channel: string,
    listener: (data: TData) => void
  ): void {
    const prefixedChannel = this.prefixChannel(channel);
    this.ipcMain.on(prefixedChannel, listener);
  }

  removeAllListeners(channel: string): void {
    const prefixedChannel = this.prefixChannel(channel);
    this.ipcMain.removeAllListeners(prefixedChannel);
  }
  
  /**
   * Prefixes the channel name with the extension ID.
   * Logs a warning if the channel already seems to contain a prefix.
   * @param channel The original channel name from the extension.
   * @returns The prefixed channel name (e.g., 'my-extension:my-channel').
   */
  private prefixChannel(channel: string): string {
    // Basic check if channel already contains a namespace separator
    if (channel.includes(':')) {
      this.logger.warn(`Channel "${channel}" might already have a namespace. Using as-is.`);
      return channel;
    }
    return `${this.extensionId}:${channel}`;
  }
}

/**
 * Secure Extension Context
 * Wraps the original extension context with security checks and sandboxing
 */

import {ExtensionContext} from '../extension.types';
import {ExtensionPermission, ExtensionSecurityService} from './extension-security.service';
import {LoggingServiceAPI} from '@services/logging.service';
import {StorageServiceAPI} from '@services/storage.service';
import {IpcServiceAPI} from '@services/ipc.service';
import {ConfigurationServiceAPI} from '@services/configuration.service';
import {GitServiceAPI} from '@services/git.service';
import {Disposable, JsonValue} from '@shared/types/common';
import {BrowserWindow} from 'electron';
import Database from 'better-sqlite3';

/**
 * Secure wrapper for Storage Service
 */
class SecureStorageService implements StorageServiceAPI {
  constructor(
    private readonly original: StorageServiceAPI,
    private readonly extensionId: string,
    private readonly security: ExtensionSecurityService,
    private readonly logger: LoggingServiceAPI
  ) {
  }
  
  async run(sql: string, params?: any[]): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_WRITE permission`);
    }
    
    // Check if SQL contains DELETE and extension has delete permission
    if (sql.toLowerCase().includes('delete') &&
      !this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_DELETE)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_DELETE permission`);
    }
    
    // Prefix table names with extension ID to ensure isolation
    const secureSQL = this.prefixTableNames(sql);
    
    return this.original.run(secureSQL, params);
  }
  
  async get(sql: string, params?: any[]): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_READ permission`);
    }
    
    const secureSQL = this.prefixTableNames(sql);
    return this.original.get(secureSQL, params);
  }
  
  async all(sql: string, params?: any[]): Promise<any[]> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_READ permission`);
    }
    
    const secureSQL = this.prefixTableNames(sql);
    return this.original.all(secureSQL, params);
  }
  
  async exec(sql: string, ...params: unknown[]): Promise<Database.RunResult> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_WRITE permission`);
    }
    
    const secureSQL = this.prefixTableNames(sql);
    return this.original.exec(secureSQL, ...params);
  }
  
  async transaction<T>(callback: () => T): Promise<T> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_WRITE permission`);
    }
    
    return this.original.transaction(callback);
  }
  
  // Additional methods required by StorageServiceAPI
  async analyzeQueryPerformance(sql: string, ...params: unknown[]): Promise<{
    queryPlan: any[];
    executionTime: number;
    recommendations: string[];
  }> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_READ permission`);
    }
    
    const secureSQL = this.prefixTableNames(sql);
    return this.original.analyzeQueryPerformance(secureSQL, ...params);
  }
  
  async getDatabaseStatistics(): Promise<{
    tableStats: {
      tableName: string;
      rowCount: number;
      pageCount: number;
      avgRowSize: number;
    }[];
    indexStats: {
      indexName: string;
      tableName: string;
      isUnique: boolean;
      columns: string;
    }[];
    pragmaInfo: {
      journalMode: string;
      foreignKeys: boolean;
      cacheSize: number;
      pageSize: number;
    };
  }> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_READ permission`);
    }
    
    return this.original.getDatabaseStatistics();
  }
  
  async getPoolStatistics(): Promise<{
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    statementCacheHits: number;
    statementCacheMisses: number;
    statementCacheSize: number;
    averageAcquireTime: number;
  }> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_READ permission`);
    }
    
    return this.original.getPoolStatistics();
  }
  
  async clearStatementCache(): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.STORAGE_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have STORAGE_WRITE permission`);
    }
    
    return this.original.clearStatementCache();
  }
  
  // BaseService methods
  async initialize(_windowGetter: () => (BrowserWindow | null)): Promise<void> {
    // Extensions don't need to initialize the storage service
    return Promise.resolve();
  }
  
  async dispose(): Promise<void> {
    // Extensions don't need to dispose the storage service
    return Promise.resolve();
  }
  
  async close(): Promise<void> {
    // Extensions cannot close the main database connection
    this.logger.warn(`Extension ${this.extensionId} attempted to close database connection`);
  }
  
  private prefixTableNames(sql: string): string {
    // Simple table name prefixing for isolation
    // In a production system, this would be more sophisticated
    return sql.replace(/\b(FROM|INTO|UPDATE|JOIN)\s+([a-zA-Z_][a-zA-Z0-9_]*)/gi,
      `$1 ${this.extensionId}_$2`);
  }
}

/**
 * Secure wrapper for IPC Service
 */
class SecureIpcService implements IpcServiceAPI {
  constructor(
    private readonly original: IpcServiceAPI,
    private readonly extensionId: string,
    private readonly security: ExtensionSecurityService,
    private readonly logger: LoggingServiceAPI
  ) {
  }
  
  handle<TArgs = unknown, TResult = unknown>(
    channel: string,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): Disposable {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.IPC_HANDLE)) {
      throw new Error(`Extension ${this.extensionId} does not have IPC_HANDLE permission`);
    }
    
    // For trusted extensions, allow them to register channels with their extension ID prefix
    // For untrusted extensions, force the prefix for isolation
    const policy = this.security.getPolicy(this.extensionId);
    const isTrusted = policy && (policy.sandboxLevel === 'moderate' || policy.sandboxLevel === 'permissive');
    
    let secureChannel: string;
    if (isTrusted && channel.startsWith('channels.')) {
      // Trusted extensions can register their standard channels
      secureChannel = `${this.extensionId}:${channel}`;
    } else if (channel.startsWith(`${this.extensionId}:`)) {
      // Channel already has extension prefix
      secureChannel = channel;
    } else {
      // Force prefix for untrusted extensions or non-standard channels
      secureChannel = `${this.extensionId}:${channel}`;
    }
    
    // Wrap handler with security checks
    const secureHandler = (args: TArgs) => {
      this.security.updateResourceUsage(this.extensionId, {
        ipcRequestsCount: (this.security.getResourceUsage(this.extensionId)?.ipcRequestsCount || 0) + 1
      });
      
      return handler(args);
    };
    
    return this.original.handle(secureChannel, secureHandler);
  }
  
  async invoke(channel: string, args: any): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.IPC_INVOKE)) {
      throw new Error(`Extension ${this.extensionId} does not have IPC_INVOKE permission`);
    }
    
    // Allow access to core channels and own channels
    const isOwnChannel = channel.startsWith(`${this.extensionId}:`);
    const isCoreChannel = channel.startsWith('core:');
    
    if (!isCoreChannel && !isOwnChannel) {
      throw new Error(`Extension ${this.extensionId} cannot access channel: ${channel}`);
    }
    
    return this.original.invoke(channel, args);
  }
  
  sendToRenderer(channel: string, data: any): void {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.IPC_BROADCAST)) {
      throw new Error(`Extension ${this.extensionId} does not have IPC_BROADCAST permission`);
    }
    
    // For trusted extensions, allow them to send to their standard channels
    const policy = this.security.getPolicy(this.extensionId);
    const isTrusted = policy && (policy.sandboxLevel === 'moderate' || policy.sandboxLevel === 'permissive');
    
    let secureChannel: string;
    if (isTrusted && !channel.startsWith(`${this.extensionId}:`)) {
      // Trusted extensions can send to their standard channels
      secureChannel = `${this.extensionId}:${channel}`;
    } else {
      // Use channel as-is if already prefixed or for untrusted extensions
      secureChannel = channel;
    }
    
    this.original.sendToRenderer(secureChannel, data);
  }
  
  on(channel: string, listener: (event: any, ...args: any[]) => void): void {
    // Extensions can only listen to their own channels
    if (!channel.startsWith(`${this.extensionId}:`)) {
      throw new Error(`Extension ${this.extensionId} cannot listen to channel: ${channel}`);
    }
    
    this.original.on(channel, listener);
  }
  
  removeAllListeners(channel: string): void {
    if (channel && !channel.startsWith(`${this.extensionId}:`)) {
      throw new Error(`Extension ${this.extensionId} cannot remove listeners from channel: ${channel}`);
    }
    
    this.original.removeAllListeners(channel);
  }
  
  // Additional methods required by IpcServiceAPI
  send<TData = unknown>(channel: string, data: TData): void {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.IPC_BROADCAST)) {
      throw new Error(`Extension ${this.extensionId} does not have IPC_BROADCAST permission`);
    }
    
    const secureChannel = `${this.extensionId}:${channel}`;
    this.original.send(secureChannel, data);
  }
  
  removeHandler(channel: string): void {
    if (!channel.startsWith(`${this.extensionId}:`)) {
      throw new Error(`Extension ${this.extensionId} cannot remove handler from channel: ${channel}`);
    }
    
    this.original.removeHandler(channel);
  }
  
  notifyShutdown(): void {
    // Extensions don't need to notify shutdown
    this.logger.info(`Extension ${this.extensionId} notified of shutdown`);
  }
  
  // BaseService methods
  async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    // Extensions don't need to initialize the IPC service
    return Promise.resolve();
  }
  
  async dispose(): Promise<void> {
    // Extensions don't need to dispose the IPC service
    return Promise.resolve();
  }
}

/**
 * Secure wrapper for Configuration Service
 */
class SecureConfigurationService implements ConfigurationServiceAPI {
  constructor(
    private readonly original: ConfigurationServiceAPI,
    private readonly extensionId: string,
    private readonly security: ExtensionSecurityService
  ) {
  }
  
  async getValue<T extends JsonValue>(key: string, defaultValue?: T): Promise<T> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.CONFIG_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have CONFIG_READ permission`);
    }
    
    // Extensions can only access their own config namespace
    const secureKey = `extensions.${this.extensionId}.${key}`;
    return this.original.getValue(secureKey, defaultValue);
  }
  
  async updateValue<T extends JsonValue>(key: string, value: T): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.CONFIG_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have CONFIG_WRITE permission`);
    }
    
    const secureKey = `extensions.${this.extensionId}.${key}`;
    return this.original.updateValue(secureKey, value);
  }
  
  getAll(): Record<string, any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.CONFIG_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have CONFIG_READ permission`);
    }
    
    // Return only extension's own configuration
    const allConfig = this.original.getAll();
    const extensionConfig = allConfig[`extensions.${this.extensionId}`];
    
    // Ensure we return a Record<string, any>
    if (typeof extensionConfig === 'object' && extensionConfig !== null && !Array.isArray(extensionConfig)) {
      return extensionConfig as Record<string, any>;
    }
    
    return {};
  }
  
  onDidChangeConfiguration(listener: (event: any) => void): Disposable {
    const originalDisposable = this.original.onDidChangeConfiguration(listener);
    // Convert utility-types Disposable to common Disposable
    return {
      dispose: async () => {
        if (typeof originalDisposable.dispose === 'function') {
          originalDisposable.dispose();
        }
      }
    };
  }
  
  // Additional methods required by ConfigurationServiceAPI
  registerSetting(declaration: any): Disposable {
    // Extensions cannot register global settings
    throw new Error(`Extension ${this.extensionId} cannot register global settings`);
  }
  
  getAllSettingsDeclarations(): any[] {
    // Extensions cannot access global settings declarations
    return [];
  }
  
  async inspect<T extends JsonValue>(key: string): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.CONFIG_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have CONFIG_READ permission`);
    }
    
    const secureKey = `extensions.${this.extensionId}.${key}`;
    return this.original.inspect(secureKey);
  }
  
  // BaseService methods
  async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    // Extensions don't need to initialize the configuration service
    return Promise.resolve();
  }
  
  async dispose(): Promise<void> {
    // Extensions don't need to dispose the configuration service
    return Promise.resolve();
  }
}

/**
 * Secure wrapper for Git Service
 */
class SecureGitService implements GitServiceAPI {
  constructor(
    private readonly original: GitServiceAPI,
    private readonly extensionId: string,
    private readonly security: ExtensionSecurityService
  ) {
  }
  
  async getStatus(repositoryPath: string): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_READ permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.getStatus!(repositoryPath);
  }
  
  async commit(repositoryPath: string, message: string): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.commit(repositoryPath, message);
  }
  
  async add(repositoryPath: string, files: string[]): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.add!(repositoryPath, files);
  }
  
  async init(repositoryPath: string): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.init!(repositoryPath);
  }
  
  async clone(url: string, targetPath: string): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.NETWORK_REQUEST)) {
      throw new Error(`Extension ${this.extensionId} does not have NETWORK_REQUEST permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, targetPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${targetPath}`);
    }
    
    return this.original.clone!(url, targetPath);
  }
  
  async getBranches(repositoryPath: string): Promise<string[]> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_READ permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.getBranches!(repositoryPath);
  }
  
  async getCurrentBranch(repositoryPath: string): Promise<string> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_READ permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.getCurrentBranch!(repositoryPath);
  }
  
  async checkoutBranch(repositoryPath: string, branchName: string): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.checkoutBranch!(repositoryPath, branchName);
  }
  
  // Additional methods required by GitServiceAPI
  async initRepo(repositoryPath: string): Promise<void> {
    return this.init(repositoryPath);
  }
  
  async status(repositoryPath: string): Promise<any> {
    return this.getStatus(repositoryPath);
  }
  
  async stage(repositoryPath: string, files: string | string[]): Promise<void> {
    return this.add(repositoryPath, Array.isArray(files) ? files : [files]);
  }
  
  async log(repositoryPath: string, options?: any): Promise<any[]> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_READ permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.log(repositoryPath, options);
  }
  
  async diff(repositoryPath: string, options?: any): Promise<any> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_READ)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_READ permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.diff(repositoryPath, options);
  }
  
  
  // Additional methods required by GitServiceAPI
  async remove(repositoryPath: string, files: string | string[]): Promise<void> {
    if (!this.security.hasPermission(this.extensionId, ExtensionPermission.GIT_WRITE)) {
      throw new Error(`Extension ${this.extensionId} does not have GIT_WRITE permission`);
    }
    
    if (!this.security.canAccessFile(this.extensionId, repositoryPath)) {
      throw new Error(`Extension ${this.extensionId} cannot access path: ${repositoryPath}`);
    }
    
    return this.original.remove(repositoryPath, files);
  }
  
  // BaseService methods
  async initialize(windowGetter: () => (BrowserWindow | null)): Promise<void> {
    // Extensions don't need to initialize the git service
    return Promise.resolve();
  }
  
  async dispose(): Promise<void> {
    // Extensions don't need to dispose the git service
    return Promise.resolve();
  }
}

/**
 * Create a secure extension context
 */
export function createSecureExtensionContext(
  originalContext: ExtensionContext,
  extensionId: string,
  security: ExtensionSecurityService,
  logger: LoggingServiceAPI
): ExtensionContext {
  // Check if this is a trusted extension
  const policy = security.getPolicy(extensionId);
  const isTrusted = policy && (policy.sandboxLevel === 'moderate' || policy.sandboxLevel === 'permissive');
  
  // For trusted extensions, use minimal security (just monitoring)
  if (isTrusted) {
    return {
      subscriptions: originalContext.subscriptions,
      
      // Use original services for trusted extensions, but with monitoring
      storage: originalContext.storage,
      ipc: originalContext.ipc,
      settings: originalContext.settings,
      git: originalContext.git,
      
      // Other services remain the same
      commands: originalContext.commands,
      views: originalContext.views,
      notifications: originalContext.notifications,
      logger: logger.createScopedLogger(extensionId),
      dialogs: originalContext.dialogs,
      context: originalContext.context,
      menus: originalContext.menus,
      editors: originalContext.editors,
      ai: originalContext.ai,
      lifecycle: originalContext.lifecycle,
    };
  }
  
  // For untrusted extensions, apply full security
  return {
    subscriptions: originalContext.subscriptions,
    
    // Secure services
    storage: new SecureStorageService(originalContext.storage, extensionId, security, logger),
    ipc: new SecureIpcService(originalContext.ipc, extensionId, security, logger),
    settings: new SecureConfigurationService(originalContext.settings, extensionId, security),
    git: new SecureGitService(originalContext.git, extensionId, security),
    
    // Services that require permission checks but don't need full wrapping
    commands: originalContext.commands, // Commands are already namespaced
    views: originalContext.views,
    notifications: originalContext.notifications,
    logger: logger.createScopedLogger(extensionId), // Scoped logger
    dialogs: originalContext.dialogs,
    context: originalContext.context,
    menus: originalContext.menus,
    editors: originalContext.editors,
    ai: originalContext.ai, // AI service can be secured separately if needed
    lifecycle: originalContext.lifecycle,
  };
}

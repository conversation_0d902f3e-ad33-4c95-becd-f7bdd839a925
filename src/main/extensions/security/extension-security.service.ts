/**
 * Extension Security Service
 * Provides sandboxing, permission management, and resource access control for extensions
 */

import { LoggingServiceAPI } from '../../services/logging.service';
import { createServiceError, ErrorSeverity } from '@shared/errors';

/**
 * Permission types for extensions
 */
export enum ExtensionPermission {
  // Core permissions
  STORAGE_READ = 'storage:read',
  STORAGE_WRITE = 'storage:write',
  STORAGE_DELETE = 'storage:delete',
  
  // File system permissions
  FILE_READ = 'file:read',
  FILE_WRITE = 'file:write',
  FILE_DELETE = 'file:delete',
  
  // Network permissions
  NETWORK_REQUEST = 'network:request',
  NETWORK_EXTERNAL = 'network:external',
  
  // IPC permissions
  IPC_HANDLE = 'ipc:handle',
  IPC_INVOKE = 'ipc:invoke',
  IPC_BROADCAST = 'ipc:broadcast',
  
  // System permissions
  SYSTEM_COMMAND = 'system:command',
  SYSTEM_PROCESS = 'system:process',
  
  // Extension permissions
  EXTENSION_API = 'extension:api',
  EXTENSION_COMMUNICATE = 'extension:communicate',
  
  // UI permissions
  UI_MENU = 'ui:menu',
  UI_COMMAND = 'ui:command',
  UI_VIEW = 'ui:view',
  UI_EDITOR = 'ui:editor',
  
  // Configuration permissions
  CONFIG_READ = 'config:read',
  CONFIG_WRITE = 'config:write',
  
  // Git permissions
  GIT_READ = 'git:read',
  GIT_WRITE = 'git:write',
  
  // AI permissions
  AI_REQUEST = 'ai:request',
  AI_TASK = 'ai:task'
}

/**
 * Resource limits for extensions
 */
export interface ExtensionResourceLimits {
  // Memory limits
  maxMemoryMB: number;
  maxHeapMB: number;
  
  // CPU limits
  maxCpuPercent: number;
  maxExecutionTimeMs: number;
  
  // Storage limits
  maxStorageMB: number;
  maxDatabaseConnections: number;
  
  // Network limits
  maxNetworkRequestsPerMinute: number;
  maxNetworkBandwidthKBps: number;
  
  // File system limits
  maxFileSize: number;
  maxFilesOpen: number;
  
  // IPC limits
  maxIpcHandlers: number;
  maxIpcRequestsPerSecond: number;
}

/**
 * Security policy for an extension
 */
export interface ExtensionSecurityPolicy {
  extensionId: string;
  permissions: ExtensionPermission[];
  resourceLimits: ExtensionResourceLimits;
  trustedDomains: string[];
  allowedFilePatterns: string[];
  deniedFilePatterns: string[];
  sandboxLevel: 'strict' | 'moderate' | 'permissive';
  requiresUserConsent: boolean;
}

/**
 * Resource usage tracking
 */
export interface ExtensionResourceUsage {
  extensionId: string;
  memoryUsageMB: number;
  cpuUsagePercent: number;
  storageUsageMB: number;
  networkRequestsCount: number;
  ipcRequestsCount: number;
  lastUpdated: number;
}

/**
 * Security violation record
 */
export interface SecurityViolation {
  extensionId: string;
  violationType: 'permission' | 'resource' | 'sandbox' | 'policy';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  details: Record<string, unknown>;
}

/**
 * Default resource limits
 */
const DEFAULT_RESOURCE_LIMITS: ExtensionResourceLimits = {
  maxMemoryMB: 100,
  maxHeapMB: 50,
  maxCpuPercent: 10,
  maxExecutionTimeMs: 30000,
  maxStorageMB: 50,
  maxDatabaseConnections: 5,
  maxNetworkRequestsPerMinute: 60,
  maxNetworkBandwidthKBps: 1024,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFilesOpen: 20,
  maxIpcHandlers: 50,
  maxIpcRequestsPerSecond: 100,
};

/**
 * Extension Security Service
 */
export class ExtensionSecurityService {
  private readonly logger: LoggingServiceAPI;
  private readonly policies = new Map<string, ExtensionSecurityPolicy>();
  private readonly resourceUsage = new Map<string, ExtensionResourceUsage>();
  private readonly violations: SecurityViolation[] = [];
  private readonly resourceMonitorInterval: NodeJS.Timeout;
  
  constructor(logger: LoggingServiceAPI) {
    this.logger = logger.createScopedLogger('ExtensionSecurityService');
    
    // Start resource monitoring
    this.resourceMonitorInterval = setInterval(() => {
      this.monitorResourceUsage();
    }, 5000); // Monitor every 5 seconds
  }

  /**
   * Register security policy for an extension
   */
  registerExtensionPolicy(policy: ExtensionSecurityPolicy): void {
    this.policies.set(policy.extensionId, policy);
    
    // Initialize resource usage tracking
    this.resourceUsage.set(policy.extensionId, {
      extensionId: policy.extensionId,
      memoryUsageMB: 0,
      cpuUsagePercent: 0,
      storageUsageMB: 0,
      networkRequestsCount: 0,
      ipcRequestsCount: 0,
      lastUpdated: Date.now(),
    });
    
    this.logger.info(`Registered security policy for extension: ${policy.extensionId}`);
  }

  /**
   * Create default security policy for an extension
   */
  createDefaultPolicy(extensionId: string, trustedExtension = false): ExtensionSecurityPolicy {
    const basePermissions = [
      ExtensionPermission.STORAGE_READ,
      ExtensionPermission.STORAGE_WRITE,
      ExtensionPermission.IPC_HANDLE,
      ExtensionPermission.IPC_INVOKE,
      ExtensionPermission.UI_VIEW,
      ExtensionPermission.UI_COMMAND,
      ExtensionPermission.CONFIG_READ,
    ];
    
    const trustedPermissions = [
      ...basePermissions,
      ExtensionPermission.STORAGE_DELETE,
      ExtensionPermission.FILE_READ,
      ExtensionPermission.FILE_WRITE,
      ExtensionPermission.NETWORK_REQUEST,
      ExtensionPermission.UI_MENU,
      ExtensionPermission.UI_EDITOR,
      ExtensionPermission.CONFIG_WRITE,
      ExtensionPermission.GIT_READ,
      ExtensionPermission.GIT_WRITE,
      ExtensionPermission.AI_REQUEST,
      ExtensionPermission.AI_TASK,
    ];
    
    return {
      extensionId,
      permissions: trustedExtension ? trustedPermissions : basePermissions,
      resourceLimits: { ...DEFAULT_RESOURCE_LIMITS },
      trustedDomains: [],
      allowedFilePatterns: [`**/${extensionId}/**`],
      deniedFilePatterns: ['**/node_modules/**', '**/.*'],
      sandboxLevel: trustedExtension ? 'moderate' : 'strict',
      requiresUserConsent: !trustedExtension,
    };
  }

  /**
   * Check if extension has permission
   */
  hasPermission(extensionId: string, permission: ExtensionPermission): boolean {
    const policy = this.policies.get(extensionId);
    if (!policy) {
      this.recordViolation(extensionId, 'policy', `No security policy found for extension`, 'high');
      return false;
    }
    
    const hasPermission = policy.permissions.includes(permission);
    if (!hasPermission) {
      this.recordViolation(extensionId, 'permission', `Missing permission: ${permission}`, 'medium');
    }
    
    return hasPermission;
  }

  /**
   * Check if extension can access a file path
   */
  canAccessFile(extensionId: string, filePath: string): boolean {
    const policy = this.policies.get(extensionId);
    if (!policy) {
      return false;
    }
    
    // Check denied patterns first
    for (const pattern of policy.deniedFilePatterns) {
      if (this.matchesPattern(filePath, pattern)) {
        this.recordViolation(extensionId, 'sandbox', `Access denied to file: ${filePath}`, 'medium');
        return false;
      }
    }
    
    // Check allowed patterns
    for (const pattern of policy.allowedFilePatterns) {
      if (this.matchesPattern(filePath, pattern)) {
        return true;
      }
    }
    
    this.recordViolation(extensionId, 'sandbox', `File access not allowed: ${filePath}`, 'medium');
    return false;
  }

  /**
   * Check if extension can make network request to domain
   */
  canAccessDomain(extensionId: string, domain: string): boolean {
    const policy = this.policies.get(extensionId);
    if (!policy) {
      return false;
    }
    
    // If no trusted domains specified, allow all (for backward compatibility)
    if (policy.trustedDomains.length === 0) {
      return true;
    }
    
    const allowed = policy.trustedDomains.some(trustedDomain => 
      domain === trustedDomain || domain.endsWith(`.${trustedDomain}`)
    );
    
    if (!allowed) {
      this.recordViolation(extensionId, 'sandbox', `Network access denied to domain: ${domain}`, 'medium');
    }
    
    return allowed;
  }

  /**
   * Check resource limits
   */
  checkResourceLimits(extensionId: string, resourceType: keyof ExtensionResourceLimits, currentValue: number): boolean {
    const policy = this.policies.get(extensionId);
    if (!policy) {
      return false;
    }
    
    const limit = policy.resourceLimits[resourceType];
    const exceeded = currentValue > limit;
    
    if (exceeded) {
      this.recordViolation(extensionId, 'resource', `Resource limit exceeded: ${resourceType} (${currentValue} > ${limit})`, 'high');
    }
    
    return !exceeded;
  }

  /**
   * Update resource usage for an extension
   */
  updateResourceUsage(extensionId: string, updates: Partial<ExtensionResourceUsage>): void {
    const current = this.resourceUsage.get(extensionId);
    if (!current) {
      return;
    }
    
    const updated = {
      ...current,
      ...updates,
      lastUpdated: Date.now(),
    };
    
    this.resourceUsage.set(extensionId, updated);
    
    // Check limits
    const policy = this.policies.get(extensionId);
    if (policy) {
      this.checkResourceLimits(extensionId, 'maxMemoryMB', updated.memoryUsageMB);
      this.checkResourceLimits(extensionId, 'maxStorageMB', updated.storageUsageMB);
    }
  }

  /**
   * Get resource usage for an extension
   */
  getResourceUsage(extensionId: string): ExtensionResourceUsage | undefined {
    return this.resourceUsage.get(extensionId);
  }

  /**
   * Get all resource usage
   */
  getAllResourceUsage(): ExtensionResourceUsage[] {
    return Array.from(this.resourceUsage.values());
  }

  /**
   * Get security violations
   */
  getViolations(extensionId?: string): SecurityViolation[] {
    if (extensionId) {
      return this.violations.filter(v => v.extensionId === extensionId);
    }
    return [...this.violations];
  }

  /**
   * Clear violations for an extension
   */
  clearViolations(extensionId: string): void {
    const index = this.violations.findIndex(v => v.extensionId === extensionId);
    if (index >= 0) {
      this.violations.splice(index, 1);
    }
  }

  /**
   * Get security policy for an extension
   */
  getPolicy(extensionId: string): ExtensionSecurityPolicy | undefined {
    return this.policies.get(extensionId);
  }

  /**
   * Update security policy for an extension
   */
  updatePolicy(extensionId: string, updates: Partial<ExtensionSecurityPolicy>): void {
    const current = this.policies.get(extensionId);
    if (!current) {
      throw createServiceError(
        'ExtensionSecurityService',
        'updatePolicy',
        `No policy found for extension: ${extensionId}`,
        { severity: ErrorSeverity.MEDIUM }
      );
    }
    
    const updated = { ...current, ...updates };
    this.policies.set(extensionId, updated);
    
    this.logger.info(`Updated security policy for extension: ${extensionId}`);
  }

  /**
   * Record a security violation
   */
  private recordViolation(
    extensionId: string,
    violationType: SecurityViolation['violationType'],
    description: string,
    severity: SecurityViolation['severity'],
    details: Record<string, unknown> = {}
  ): void {
    const violation: SecurityViolation = {
      extensionId,
      violationType,
      description,
      severity,
      timestamp: Date.now(),
      details,
    };
    
    this.violations.push(violation);
    
    // Keep only last 1000 violations
    if (this.violations.length > 1000) {
      this.violations.splice(0, this.violations.length - 1000);
    }
    
    this.logger.warn(`Security violation [${severity}]: ${description}`, { extensionId, details });
  }

  /**
   * Simple pattern matching (supports * wildcards)
   */
  private matchesPattern(path: string, pattern: string): boolean {
    // Handle ** and * patterns properly
    const regexPattern = pattern
      .replace(/\*\*/g, '___DOUBLESTAR___')  // First replace ** with placeholder
      .replace(/\*/g, '___SINGLESTAR___')  // Replace remaining * with placeholder
      .replace(/[.+^${}()|[\]\\]/g, '\\$&')  // Escape special regex chars
      .replace(/___DOUBLESTAR___/g, '.*')  // Replace ** with path matcher
      .replace(/___SINGLESTAR___/g, '[^/]*');  // Replace * with filename matcher

    try {
      const regex = new RegExp(`^${regexPattern}$`);
      return regex.test(path);
    } catch (error) {
      // If regex is invalid, return false
      this.logger.warn(`Invalid pattern: ${pattern}`, error);
      return false;
    }
  }

  /**
   * Monitor resource usage (placeholder for actual implementation)
   */
  private monitorResourceUsage(): void {
    // In a real implementation, this would:
    // 1. Check memory usage per extension
    // 2. Monitor CPU usage
    // 3. Track storage usage
    // 4. Monitor network activity
    // 5. Check file handles
    
    // For now, we'll just log that monitoring is active
    this.logger.debug('Resource monitoring cycle completed');
  }

  /**
   * Dispose the security service
   */
  dispose(): void {
    if (this.resourceMonitorInterval) {
      clearInterval(this.resourceMonitorInterval);
    }
    
    this.policies.clear();
    this.resourceUsage.clear();
    this.violations.length = 0;
    
    this.logger.info('Extension security service disposed');
  }
}

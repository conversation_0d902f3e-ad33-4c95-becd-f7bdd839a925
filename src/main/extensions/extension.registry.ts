import * as path from 'path';
import * as fs from 'fs/promises';
import {app} from 'electron';
import {logger} from '@services/logging.service';
import type {CoreServicesAPI} from '../service.manager.js';
import {ExtensionIpcService} from "@main/extensions/extensionIpc.service";
import {ContributionHandlersRegistry} from './contributionHandlers.registry';
import {ViewContributionHandler} from './handlers/viewContributionHandler';
import {ViewContainerContributionHandler} from './handlers/viewContainerContributionHandler';
import {EditorContributionHandler} from './handlers/editorContributionHandler';
import {MenuContributionHandler} from './handlers/menuContributionHandler';
import {AITaskContributionHandler} from './handlers/aiTaskContributionHandler';
import {CommandContributionHandler} from './handlers/commandContributionHandler';
import {ExtensionApi, ExtensionContext, ExtensionManifest, ExtensionModule, LoadedExtension} from './extension.types';
import {ExtensionSecurityService} from './security/extension-security.service';
import {ExtensionSandboxManager} from './security/extension-sandbox.manager';
import {createSecureExtensionContext} from './security/secure-extension-context';

export class ExtensionRegistry {
  private readonly extensions = new Map<string, LoadedExtension>();
  // Полный объект CoreServicesAPI, предоставленный ServiceManager
  private readonly coreApis: CoreServicesAPI;
  private readonly extensionsSourcePath: string;
  private readonly isPackaged: boolean;
  private readonly mainBuildPath: string; // Путь к директории сборки main (.vite/build/main или resources/app)
  private readonly contributionHandlersRegistry: ContributionHandlersRegistry;
  private readonly securityService: ExtensionSecurityService;
  private readonly sandboxManager: ExtensionSandboxManager;
  
  constructor(coreApis: CoreServicesAPI) {
    this.isPackaged = app.isPackaged;
    const appPath = app.getAppPath();
    this.extensionsSourcePath = path.join(process.cwd(), 'src', 'extensions');
    
    if (this.isPackaged) {
      // В упакованном приложении appPath указывает на корень app.asar или app/
      this.mainBuildPath = appPath;
    } else {
      // В режиме разработки __dirname указывает на .vite/build/main
      this.mainBuildPath = __dirname;
    }
    this.coreApis = coreApis;
    this.contributionHandlersRegistry = new ContributionHandlersRegistry(logger);

    // Initialize security services
    this.securityService = new ExtensionSecurityService(logger);
    this.sandboxManager = new ExtensionSandboxManager(logger, this.securityService);

    this.registerContributionHandlers();
  }

  async discoverExtensions(): Promise<void> {
    try {
      logger.info(`[ExtensionRegistry] Поиск расширений в: ${this.extensionsSourcePath}`);
      const entries = await fs.readdir(this.extensionsSourcePath, {withFileTypes: true});
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const extensionDirName = entry.name;
          const extensionDirPath = path.join(this.extensionsSourcePath, extensionDirName);
          const manifestPath = path.join(extensionDirPath, 'package.json');
          try {
            const manifestContent = await fs.readFile(manifestPath, 'utf-8');
            const manifest = JSON.parse(manifestContent) as ExtensionManifest;
            if (!manifest.name || !manifest.publisher || !manifest.main) {
              logger.error(`[ExtensionRegistry] Некорректный манифест в ${manifestPath}. Отсутствуют обязательные поля (name, publisher, main).`);
              continue;
            }
            const extensionId = `${manifest.publisher}.${manifest.name}`;
            // manifest.main — путь относительно исходной директории расширения
            // Для сборки нужен путь относительно build-директории
            const compiledRelativePath = path.join('extensions', extensionDirName, manifest.main);
            const compiledAbsolutePath = path.join(this.mainBuildPath, compiledRelativePath);
            logger.info(`[ExtensionRegistry] Найдено расширение: ${extensionId} (v${manifest.version})`);
            logger.info(`[ExtensionRegistry]   Манифест: ${manifestPath}`);
            logger.info(`[ExtensionRegistry]   Скомпилированный файл: ${compiledAbsolutePath}`);
            if (this.extensions.has(extensionId)) {
              logger.warn(`[ExtensionRegistry] Дублирующийся ID расширения: ${extensionId}. Пропуск ${manifestPath}.`);
              continue;
            }
            // Initialize sandbox for the extension
            this.sandboxManager.initializeSandbox(extensionId, manifest);

            this.extensions.set(extensionId, {
              id: extensionId,
              manifest: manifest,
              module: {activate: async () => undefined},
              isActive: false,
              subscriptions: [],
              compiledPath: compiledAbsolutePath
            });
            
            const extension = this.extensions.get(extensionId);
            if (extension) {
              this.processContributions(extension);
            } else {
              logger.error(`[ExtensionRegistry] Failed to retrieve extension ${extensionId} after setting it`);
            }
          } catch (error) {
            const err = error as { code?: string };
            if (err.code === 'ENOENT') {
              logger.debug(`[ExtensionRegistry] Не найден package.json в ${extensionDirPath}, пропуск.`);
            } else if (error instanceof SyntaxError) {
              logger.error(`[ExtensionRegistry] Ошибка парсинга манифеста ${manifestPath}:`, error);
            } else {
              logger.error(`[ExtensionRegistry] Ошибка обработки директории ${extensionDirPath}:`, error);
            }
          }
        }
      }
      logger.info(`[ExtensionRegistry] Поиск завершён. Найдено ${this.extensions.size} валидных расширений.`);
    } catch (error) {
      logger.error('[ExtensionRegistry] Ошибка чтения директории расширений:', error);
    }
  }

  async activateAll(): Promise<void> {
    logger.info('[ExtensionRegistry] Активация всех расширений...');
    for (const extensionId of this.extensions.keys()) {
      await this.activateExtension(extensionId);
    }
    logger.info('[ExtensionRegistry] Все расширения активированы.');
  }

  async activateExtension(extensionId: string): Promise<ExtensionApi | undefined> {
    const loadedExtension = this.extensions.get(extensionId);
    if (!loadedExtension) {
      logger.warn(`[ExtensionRegistry] Расширение ${extensionId} не найдено.`);
      return undefined;
    }
    if (loadedExtension.isActive) {
      return loadedExtension.api;
    }
    logger.info(`[ExtensionRegistry] Активация ${extensionId}...`);
    // Используем сохранённый абсолютный путь к скомпилированному файлу
    const compiledIndexPath = loadedExtension.compiledPath;
    logger.info(`[ExtensionRegistry] Импорт модуля из: ${compiledIndexPath}`);
    try {
      await fs.access(compiledIndexPath);
      const modulePathUrl = 'file://' + compiledIndexPath.replace(/\\/g, '/');
      logger.info(`[ExtensionRegistry] Импорт по URL: ${modulePathUrl}`);
      loadedExtension.module = await import(modulePathUrl) as ExtensionModule;
      const extensionIpcService = new ExtensionIpcService(logger, this.coreApis.ipc, extensionId);
      const extensionLogger = logger.createScopedLogger(extensionId);

      // Create original context
      const originalContext: ExtensionContext = {
        subscriptions: loadedExtension.subscriptions,
        commands: this.coreApis.commands,
        views: this.coreApis.views,
        storage: this.coreApis.storage,
        settings: this.coreApis.configuration,
        notifications: this.coreApis.notifications,
        dialogs: this.coreApis.dialogs,
        context: this.coreApis.context,
        menus: this.coreApis.menus,
        editors: this.coreApis.editors,
        git: this.coreApis.git,
        ai: this.coreApis.ai,
        logger: extensionLogger,
        ipc: extensionIpcService,
        lifecycle: this.coreApis.lifecycle,
      };

      // Create secure context with sandboxing
      const secureContext = createSecureExtensionContext(originalContext, extensionId, this.securityService, extensionLogger);

      // Execute extension activation in sandbox
      loadedExtension.api = await this.sandboxManager.executeInSandbox(
        extensionId,
        () => loadedExtension.module.activate(secureContext)
      );
      loadedExtension.isActive = true;
      logger.info(`[ExtensionRegistry] Расширение ${extensionId} успешно активировано.`);
      return loadedExtension.api;
    } catch (error) {
      logger.error(`[ExtensionRegistry] Не удалось активировать расширение ${extensionId}:`, error);
      this.extensions.delete(extensionId);
      return undefined;
    }
  }

  async getApi<T>(extensionId: string): Promise<T | undefined> {
    const loadedExtension = this.extensions.get(extensionId);
    if (!loadedExtension) {
      logger.warn(`[ExtensionRegistry] Запрошено расширение ${extensionId}, но оно не найдено.`);
      return undefined;
    }
    if (loadedExtension.isActive) {
      return loadedExtension.api as T;
    } else {
      const api = await this.activateExtension(extensionId);
      return api as T;
    }
  }

  async deactivateAll(): Promise<void> {
    logger.info('[ExtensionRegistry] Деактивация всех расширений...');
    for (const loadedExtension of this.extensions.values()) {
      if (loadedExtension.isActive && loadedExtension.module.deactivate) {
        try {
          logger.info(`[ExtensionRegistry] Деактивация ${loadedExtension.id}...`);
          await loadedExtension.module.deactivate();
          loadedExtension.subscriptions.forEach(sub => sub.dispose());
          loadedExtension.subscriptions.length = 0;
          logger.info(`[ExtensionRegistry] Расширение ${loadedExtension.id} деактивировано.`);
        } catch (error) {
          logger.error(`[ExtensionRegistry] Ошибка при деактивации расширения ${loadedExtension.id}:`, error);
        }
      }
      loadedExtension.isActive = false;
    }

    // Dispose security services
    this.sandboxManager.dispose();
    this.securityService.dispose();

    logger.info('[ExtensionRegistry] Все расширения деактивированы.');
  }

  /**
   * Get security service for external access
   */
  getSecurityService(): ExtensionSecurityService {
    return this.securityService;
  }

  /**
   * Get sandbox manager for external access
   */
  getSandboxManager(): ExtensionSandboxManager {
    return this.sandboxManager;
  }

  /**
   * Регистрирует обработчики точек вклада в реестре обработчиков.
   */
  private registerContributionHandlers(): void {
    this.contributionHandlersRegistry.registerHandler(
      new CommandContributionHandler(logger, this.coreApis.commands)
    );
    this.contributionHandlersRegistry.registerHandler(
      new ViewContainerContributionHandler(logger, this.coreApis.views)
    );
    this.contributionHandlersRegistry.registerHandler(
      new ViewContributionHandler(logger, this.coreApis.views)
    );
    this.contributionHandlersRegistry.registerHandler(
      new EditorContributionHandler(logger, this.coreApis.editors)
    );
    this.contributionHandlersRegistry.registerHandler(
      new MenuContributionHandler(logger, this.coreApis.menus)
    );
    this.contributionHandlersRegistry.registerHandler(
      new AITaskContributionHandler(logger, this.coreApis.ai)
    );
  }

  /**
   * Обрабатывает точки вклада из манифеста расширения.
   * @param extension Загруженное расширение
   */
  private processContributions(extension: LoadedExtension): void {
    const {id: extensionId, manifest, subscriptions} = extension;
    if (!manifest.contributes) {
      return;
    }
    logger.info(`[ExtensionRegistry] Обработка точек вклада для ${extensionId}...`);
    const disposables = this.contributionHandlersRegistry.processContributions(extensionId, manifest);
    subscriptions.push(...disposables);
    logger.info(`[ExtensionRegistry] Обработка точек вклада для ${extensionId} завершена.`);
  }
}

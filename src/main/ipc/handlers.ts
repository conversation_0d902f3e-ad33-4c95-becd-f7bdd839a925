import {app, BrowserWindow, ipcMain} from 'electron';
import type {ServiceManager} from '@main/service.manager';
import {logger} from '@services/logging.service';
import {IpcChannels} from '@shared/constants/ipc-channels';

import {AppGetVersionResult, LogMessageData, WindowIsMaximizedResult,} from '@shared/types/ipc';
import {ErrorInfo} from '@shared/types/error-handling';
import { ValidationSchemas, Validator } from '@shared/validation';

/**
 * Registers CORE IPC handlers for communication with the Renderer process.
 * Uses prefixes like 'core.' for clarity.
 * @param registry Instance of ServiceManager to access core services.
 * @param getCurrentWindow Function to get the current BrowserWindow instance.
 */
export function registerIpcHandlers(
  registry: ServiceManager,
  getCurrentWindow: () => BrowserWindow | null,
) {
  // Get service instances (assuming they exist after ServiceManager construction)
  
  logger.info('Registering CORE IPC handlers with prefixes...');
  
  registry.ipc.handle(IpcChannels.LOG_MESSAGE, Validator.createValidatedHandler(
    ValidationSchemas.LogMessage,
    (payload: LogMessageData) => {
      const {level, message, details} = payload;
      try {
        const meta = typeof details === 'object' && details !== null ? details as Record<string, unknown> : undefined;
        switch (level) {
          case 'debug':
            logger.info(message, meta);
            break;
          case 'info':
            logger.info(message, meta);
            break;
          case 'warn':
            logger.warn(message, meta);
            break;
          case 'error':
            logger.error(message, meta);
            break;
          default:
            logger.warn(`Received log message with unknown level '${level}': ${message}`, meta);
            break;
        }
      } catch (e) {
        console.error(`Error handling '${IpcChannels.LOG_MESSAGE}' IPC:`, e);
      }
    }
  ));
  
  // --- Window Controls ---
  registry.ipc.handle<never, string>(IpcChannels.WINDOW_GET_PLATFORM, () => process.platform); // Используем константу
  registry.ipc.handle<never, WindowIsMaximizedResult>(IpcChannels.WINDOW_IS_MAXIMIZED, () => getCurrentWindow()?.isMaximized() ?? false); // Используем константу
  
  // Возвращаем использование ipcMain.on для fire-and-forget действий
  ipcMain.on(IpcChannels.WINDOW_MINIMIZE, () => getCurrentWindow()?.minimize()); // Используем константу
  ipcMain.on(IpcChannels.WINDOW_MAXIMIZE, () => getCurrentWindow()?.maximize()); // Используем константу
  ipcMain.on(IpcChannels.WINDOW_UNMAXIMIZE, () => getCurrentWindow()?.unmaximize()); // Используем константу
  ipcMain.on(IpcChannels.WINDOW_CLOSE, () => getCurrentWindow()?.close()); // Используем константу
  
  // --- App Info ---
  registry.ipc.handle<never, AppGetVersionResult>(IpcChannels.APP_GET_VERSION, () => { // Используем константу
    if (!app) throw new Error('Electron App module not available');
    return app.getVersion();
  });

  // --- Error Reporting ---
  registry.ipc.handle<Partial<ErrorInfo>, undefined>(IpcChannels.ERROR_REPORT, Validator.createValidatedHandler(
    ValidationSchemas.ErrorReport,
    (errorData) => {
    try {
      // Log the error with appropriate severity
      const logLevel = errorData.severity === 'critical' || errorData.severity === 'high' ? 'error' : 'warn';
      const logMessage = `[Error Report] ${errorData.source || 'unknown'}: ${errorData.message}`;
      const logMeta = {
        errorId: errorData.id,
        severity: errorData.severity,
        timestamp: errorData.timestamp,
        stack: errorData.stack,
        metadata: errorData.metadata,
      };

      if (logLevel === 'error') {
        logger.error(logMessage, logMeta);
      } else {
        logger.warn(logMessage, logMeta);
      }

      // TODO: In the future, this could be extended to:
      // - Store errors in a database for analysis
      // - Send errors to an external monitoring service
      // - Trigger recovery mechanisms for critical errors
      // - Update application health metrics

    } catch (e) {
      console.error(`Error handling '${IpcChannels.ERROR_REPORT}' IPC:`, e);
      // Don't throw here to avoid infinite error loops
    }
  }));

  logger.info('CORE IPC handlers registered.');
}

/**
 * Represents the unique identifier for an error record.
 */
export type ErrorId = string & { readonly __brand: 'ErrorId' };

/**
 * Creates an ErrorId from a string
 */
export function createErrorId(id: string): ErrorId {
  return id as <PERSON>rrorId;
}

/**
 * Defines severity levels for errors.
 */
export enum ErrorSeverity {
  /** Non-critical error, does not affect application operation. */
  LOW = 'low',
  /** Error that might affect some functionality. */
  MEDIUM = 'medium',
  /** Serious error affecting core functionality. */
  HIGH = 'high',
  /** Critical error potentially requiring application restart. */
  CRITICAL = 'critical'
}

/**
 * Structure for storing detailed error information.
 */
export interface ErrorInfo {
  /** Unique identifier for the error instance. @readonly */
  readonly id: ErrorId;
  /** Error message. @readonly */
  readonly message: string;
  /** Call stack, if available. @readonly */
  readonly stack?: string;
  /** Timestamp when the error occurred (e.g., Date.now()). @readonly */
  readonly timestamp: number;
  /** Source of the error (e.g., service name, extension ID). @readonly */
  readonly source: string;
  /** Severity level of the error. @readonly */
  readonly severity: ErrorSeverity;
  /** Additional arbitrary data related to the error. @readonly */
  readonly metadata?: Readonly<Record<string, unknown>>;
  /** Flag indicating if the error has been handled (e.g., logged, shown to user). */
  handled: boolean;
  /** Flag indicating if a recovery mechanism was attempted. */
  recoveryAttempted?: boolean;
  /** Flag indicating if the recovery attempt was successful. */
  recoverySuccessful?: boolean;
}

/**
 * Statistics about collected errors.
 */
export interface ErrorStats {
  /** Total number of errors recorded. @readonly */
  readonly total: number;
  /** Count of errors grouped by severity. @readonly */
  readonly bySeverity: Readonly<Record<ErrorSeverity, number>>;
  /** Count of errors grouped by source. @readonly */
  readonly bySource: Readonly<Record<string, number>>;
  /** Total number of recovery attempts made. @readonly */
  readonly recoveryAttempts: number;
  /** Total number of successful recovery attempts. @readonly */
  readonly recoverySuccesses: number;
}

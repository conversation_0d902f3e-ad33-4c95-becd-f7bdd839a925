/**
 * Standardized error handling system for the application
 * Provides consistent error types, codes, and handling patterns
 */

import { ErrorSeverity } from '@shared/types/error-handling';

/**
 * Standard error codes used throughout the application
 */
export enum ErrorCode {
  // Generic errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  INVALID_ARGUMENT = 'INVALID_ARGUMENT',
  INVALID_STATE = 'INVALID_STATE',
  NOT_IMPLEMENTED = 'NOT_IMPLEMENTED',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SCHEMA_VALIDATION_ERROR = 'SCHEMA_VALIDATION_ERROR',
  
  // Resource errors
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  RESOURCE_EXHAUSTED = 'RESOURCE_EXHAUSTED',
  
  // Permission errors
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  
  // Storage errors
  STORAGE_ERROR = 'STORAGE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  FILE_SYSTEM_ERROR = 'FILE_SYSTEM_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  
  // Service errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  SERVICE_ERROR = 'SERVICE_ERROR',
  INITIALIZATION_ERROR = 'INITIALIZATION_ERROR',
  
  // Git errors
  GIT_ERROR = 'GIT_ERROR',
  GIT_REPOSITORY_ERROR = 'GIT_REPOSITORY_ERROR',
  GIT_OPERATION_ERROR = 'GIT_OPERATION_ERROR',
  
  // Extension errors
  EXTENSION_ERROR = 'EXTENSION_ERROR',
  EXTENSION_NOT_FOUND = 'EXTENSION_NOT_FOUND',
  EXTENSION_LOAD_ERROR = 'EXTENSION_LOAD_ERROR',
  
  // IPC errors
  IPC_ERROR = 'IPC_ERROR',
  IPC_TIMEOUT = 'IPC_TIMEOUT',
  IPC_VALIDATION_ERROR = 'IPC_VALIDATION_ERROR',
}

/**
 * Error categories for grouping related error types
 */
export enum ErrorCategory {
  VALIDATION = 'validation',
  RESOURCE = 'resource',
  PERMISSION = 'permission',
  STORAGE = 'storage',
  NETWORK = 'network',
  SERVICE = 'service',
  GIT = 'git',
  EXTENSION = 'extension',
  IPC = 'ipc',
  SYSTEM = 'system',
}

/**
 * Base error class that all application errors should extend
 */
export abstract class BaseError extends Error {
  public readonly code: ErrorCode;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly timestamp: number;
  public readonly source: string;
  public readonly metadata: Record<string, unknown>;
  public readonly isRetryable: boolean;

  constructor(
    code: ErrorCode,
    message: string,
    options: {
      category: ErrorCategory;
      severity?: ErrorSeverity;
      source?: string;
      metadata?: Record<string, unknown>;
      isRetryable?: boolean;
      cause?: Error;
    }
  ) {
    super(message);
    
    this.name = this.constructor.name;
    this.code = code;
    this.category = options.category;
    this.severity = options.severity ?? ErrorSeverity.MEDIUM;
    this.timestamp = Date.now();
    this.source = options.source ?? 'unknown';
    this.metadata = options.metadata ?? {};
    this.isRetryable = options.isRetryable ?? false;

    // Preserve the original error as cause if provided
    if (options.cause) {
      this.cause = options.cause;
      // Include original stack trace in metadata
      this.metadata.originalStack = options.cause.stack;
    }

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype);
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Convert error to a plain object for serialization
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      code: this.code,
      category: this.category,
      message: this.message,
      severity: this.severity,
      timestamp: this.timestamp,
      source: this.source,
      metadata: this.metadata,
      isRetryable: this.isRetryable,
      stack: this.stack,
    };
  }

  /**
   * Create a user-friendly error message
   */
  getUserMessage(): string {
    // Override in subclasses to provide user-friendly messages
    return this.message;
  }

  /**
   * Check if this error should be reported to external monitoring
   */
  shouldReport(): boolean {
    return this.severity === ErrorSeverity.HIGH || this.severity === ErrorSeverity.CRITICAL;
  }
}

/**
 * Validation error for input validation failures
 */
export class ValidationError extends BaseError {
  constructor(
    message: string,
    options: {
      field?: string;
      value?: unknown;
      source?: string;
      metadata?: Record<string, unknown>;
    } = {}
  ) {
    super(ErrorCode.VALIDATION_ERROR, message, {
      category: ErrorCategory.VALIDATION,
      severity: ErrorSeverity.LOW,
      source: options.source,
      metadata: {
        field: options.field,
        value: options.value,
        ...options.metadata,
      },
    });
  }

  getUserMessage(): string {
    const field = this.metadata.field as string;
    return field ? `Invalid ${field}: ${this.message}` : `Validation error: ${this.message}`;
  }
}

/**
 * Resource not found error
 */
export class NotFoundError extends BaseError {
  constructor(
    resource: string,
    identifier: string,
    options: {
      source?: string;
      metadata?: Record<string, unknown>;
    } = {}
  ) {
    super(ErrorCode.NOT_FOUND, `${resource} not found: ${identifier}`, {
      category: ErrorCategory.RESOURCE,
      severity: ErrorSeverity.LOW,
      source: options.source,
      metadata: {
        resource,
        identifier,
        ...options.metadata,
      },
    });
  }

  getUserMessage(): string {
    const resource = this.metadata.resource as string;
    return `${resource} not found. Please check if it exists and try again.`;
  }
}

/**
 * Storage operation error
 */
export class StorageError extends BaseError {
  constructor(
    message: string,
    options: {
      operation?: string;
      source?: string;
      metadata?: Record<string, unknown>;
      cause?: Error;
      isRetryable?: boolean;
    } = {}
  ) {
    super(ErrorCode.STORAGE_ERROR, message, {
      category: ErrorCategory.STORAGE,
      severity: ErrorSeverity.MEDIUM,
      source: options.source,
      metadata: {
        operation: options.operation,
        ...options.metadata,
      },
      cause: options.cause,
      isRetryable: options.isRetryable ?? true, // Storage errors are often retryable
    });
  }

  getUserMessage(): string {
    return 'A storage error occurred. Please try again.';
  }
}

/**
 * Service error for service-level failures
 */
export class ServiceError extends BaseError {
  constructor(
    serviceName: string,
    message: string,
    options: {
      operation?: string;
      severity?: ErrorSeverity;
      metadata?: Record<string, unknown>;
      cause?: Error;
      isRetryable?: boolean;
    } = {}
  ) {
    super(ErrorCode.SERVICE_ERROR, message, {
      category: ErrorCategory.SERVICE,
      severity: options.severity ?? ErrorSeverity.MEDIUM,
      source: serviceName,
      metadata: {
        service: serviceName,
        operation: options.operation,
        ...options.metadata,
      },
      cause: options.cause,
      isRetryable: options.isRetryable ?? false,
    });
  }

  getUserMessage(): string {
    const service = this.metadata.service as string;
    return `${service} service is currently unavailable. Please try again later.`;
  }
}

/**
 * Git operation error
 */
export class GitError extends BaseError {
  constructor(
    message: string,
    options: {
      operation?: string;
      repository?: string;
      source?: string;
      metadata?: Record<string, unknown>;
      cause?: Error;
    } = {}
  ) {
    super(ErrorCode.GIT_ERROR, message, {
      category: ErrorCategory.GIT,
      severity: ErrorSeverity.MEDIUM,
      source: options.source ?? 'GitService',
      metadata: {
        operation: options.operation,
        repository: options.repository,
        ...options.metadata,
      },
      cause: options.cause,
      isRetryable: true, // Git operations are often retryable
    });
  }

  getUserMessage(): string {
    const operation = this.metadata.operation as string;
    return operation 
      ? `Git ${operation} failed. Please check your repository and try again.`
      : 'Git operation failed. Please try again.';
  }
}

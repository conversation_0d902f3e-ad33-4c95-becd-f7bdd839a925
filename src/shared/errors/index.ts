/**
 * Standardized error handling system
 * 
 * This module provides a comprehensive error handling framework with:
 * - Standardized error types and codes
 * - Error categorization and severity levels
 * - Utility functions for error conversion and handling
 * - Retry mechanisms and error aggregation
 * - IPC error serialization
 */

// Base error classes and types
export {
  BaseError,
  ValidationError,
  NotFoundError,
  StorageError,
  ServiceError,
  GitError,
  ErrorCode,
  ErrorCategory,
} from './base';

// Utility functions and helpers
export {
  isBaseError,
  isError,
  isErrorLike,
  toBaseError,
  toIpcError,
  errorToIpcError,
  createValidationError,
  createNotFoundError,
  createStorageError,
  createServiceError,
  createGitError,
  withErrorHandling,
  withAsyncErrorHandling,
  withRetry,
  ErrorAggregator,
  ErrorContext,
  globalErrorContext,
} from './utils';

// Re-export error severity from shared types
export { ErrorSeverity } from '@shared/types/error-handling';

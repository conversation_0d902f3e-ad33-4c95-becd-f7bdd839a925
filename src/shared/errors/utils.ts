/**
 * Error handling utilities and helper functions
 */

import { BaseError, ValidationError, NotFoundError, StorageError, ServiceError, GitError } from './base';
import { ErrorSeverity } from '@shared/types/error-handling';
import { IpcErrorData } from '@shared/types/ipc';

/**
 * Type guard to check if an error is a BaseError
 */
export function isBaseError(error: unknown): error is BaseError {
  return error instanceof BaseError;
}

/**
 * Type guard to check if an error is an Error instance
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Type guard to check if an object has error-like properties
 */
export function isErrorLike(obj: unknown): obj is { message: string; stack?: string; name?: string } {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'message' in obj &&
    typeof (obj as { message: unknown }).message === 'string'
  );
}

/**
 * Convert any error to a BaseError
 */
export function toBaseError(error: unknown, source?: string): BaseError {
  if (isBaseError(error)) {
    return error;
  }

  if (isError(error)) {
    return new ServiceError(source ?? 'unknown', error.message, {
      cause: error,
      metadata: {
        originalName: error.name,
      },
    });
  }

  if (isErrorLike(error)) {
    return new ServiceError(source ?? 'unknown', error.message, {
      metadata: {
        originalName: error.name,
        originalStack: error.stack,
      },
    });
  }

  return new ServiceError(source ?? 'unknown', String(error), {
    metadata: {
      originalValue: error,
    },
  });
}

/**
 * Convert a BaseError to IpcErrorData format
 */
export function toIpcError(error: BaseError): IpcErrorData {
  return {
    code: error.code,
    message: error.message,
    details: {
      category: error.category,
      severity: error.severity,
      timestamp: error.timestamp,
      source: error.source,
      metadata: error.metadata,
      isRetryable: error.isRetryable,
      stack: error.stack,
    },
  };
}

/**
 * Convert any error to IpcErrorData format
 */
export function errorToIpcError(error: unknown, source?: string): IpcErrorData {
  const baseError = toBaseError(error, source);
  return toIpcError(baseError);
}

/**
 * Create a validation error with field information
 */
export function createValidationError(
  field: string,
  value: unknown,
  message: string,
  source?: string
): ValidationError {
  return new ValidationError(message, {
    field,
    value,
    source,
  });
}

/**
 * Create a not found error for a specific resource
 */
export function createNotFoundError(
  resource: string,
  identifier: string,
  source?: string
): NotFoundError {
  return new NotFoundError(resource, identifier, { source });
}

/**
 * Create a storage error with operation context
 */
export function createStorageError(
  operation: string,
  message: string,
  source?: string,
  cause?: Error
): StorageError {
  return new StorageError(message, {
    operation,
    source,
    cause,
  });
}

/**
 * Create a service error with service context
 */
export function createServiceError(
  serviceName: string,
  operation: string,
  message: string,
  options: {
    severity?: ErrorSeverity;
    cause?: Error;
    isRetryable?: boolean;
    metadata?: Record<string, unknown>;
  } = {}
): ServiceError {
  return new ServiceError(serviceName, message, {
    operation,
    ...options,
  });
}

/**
 * Create a Git error with repository context
 */
export function createGitError(
  operation: string,
  repository: string,
  message: string,
  cause?: Error
): GitError {
  return new GitError(message, {
    operation,
    repository,
    cause,
  });
}

/**
 * Wrap a function to catch and convert errors to BaseError
 */
export function withErrorHandling<T extends unknown[], R>(
  fn: (...args: T) => R,
  source: string
): (...args: T) => R {
  return (...args: T): R => {
    try {
      return fn(...args);
    } catch (error) {
      throw toBaseError(error, source);
    }
  };
}

/**
 * Wrap an async function to catch and convert errors to BaseError
 */
export function withAsyncErrorHandling<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  source: string
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw toBaseError(error, source);
    }
  };
}

/**
 * Retry function with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    shouldRetry?: (error: unknown) => boolean;
  } = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    shouldRetry = (error) => isBaseError(error) && error.isRetryable,
  } = options;

  let lastError: unknown;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts || !shouldRetry(error)) {
        throw error;
      }
      
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Error aggregation for collecting multiple errors
 */
export class ErrorAggregator {
  private errors: BaseError[] = [];

  add(error: unknown, source?: string): void {
    this.errors.push(toBaseError(error, source));
  }

  addValidation(field: string, value: unknown, message: string, source?: string): void {
    this.add(createValidationError(field, value, message, source));
  }

  hasErrors(): boolean {
    return this.errors.length > 0;
  }

  getErrors(): BaseError[] {
    return [...this.errors];
  }

  getErrorCount(): number {
    return this.errors.length;
  }

  clear(): void {
    this.errors = [];
  }

  throwIfErrors(message = 'Multiple errors occurred'): void {
    if (this.hasErrors()) {
      const aggregatedError = new ServiceError('ErrorAggregator', message, {
        metadata: {
          errorCount: this.errors.length,
          errors: this.errors.map(e => e.toJSON()),
        },
        severity: this.getHighestSeverity(),
      });
      throw aggregatedError;
    }
  }

  private getHighestSeverity(): ErrorSeverity {
    if (this.errors.length === 0) return ErrorSeverity.LOW;
    
    const severityOrder = [ErrorSeverity.LOW, ErrorSeverity.MEDIUM, ErrorSeverity.HIGH, ErrorSeverity.CRITICAL];
    let highest = ErrorSeverity.LOW;
    
    for (const error of this.errors) {
      const currentIndex = severityOrder.indexOf(error.severity);
      const highestIndex = severityOrder.indexOf(highest);
      if (currentIndex > highestIndex) {
        highest = error.severity;
      }
    }
    
    return highest;
  }
}

/**
 * Error context for tracking error propagation
 */
export class ErrorContext {
  private context: Record<string, unknown> = {};

  set(key: string, value: unknown): this {
    this.context[key] = value;
    return this;
  }

  get(key: string): unknown {
    return this.context[key];
  }

  getAll(): Record<string, unknown> {
    return { ...this.context };
  }

  wrap(error: BaseError): BaseError {
    const wrappedError = new (error.constructor as new (...args: unknown[]) => BaseError)(
      error.code,
      error.message,
      {
        category: error.category,
        severity: error.severity,
        source: error.source,
        metadata: {
          ...error.metadata,
          context: this.context,
        },
        isRetryable: error.isRetryable,
        cause: error,
      }
    );
    return wrappedError;
  }
}

/**
 * Global error context instance
 */
export const globalErrorContext = new ErrorContext();

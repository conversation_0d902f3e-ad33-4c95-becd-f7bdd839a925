/**
 * Validation utility functions
 * Re-exports and convenience functions for the validation system
 */

import { z } from 'zod';
import { Validator } from './validator';

/**
 * Convenience function for validating input data
 * This is a simple wrapper around Validator.validateOrThrow for easier imports
 */
export function validateInput<T>(data: unknown, schema: z.ZodSchema<T>): T {
  return Validator.validateOrThrow(schema, data);
}

/**
 * Re-export commonly used validation functions
 */
export { Validator } from './validator';
export { ValidationError } from './validator';
export type { ValidationResult } from './validator';

/**
 * Re-export validation decorators and type guards
 */
export { ValidateArgs, isValidationSuccess, isValidationError } from './validator';

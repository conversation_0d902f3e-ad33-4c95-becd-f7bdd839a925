import { z } from 'zod';

// --- Common Validation Schemas ---

/**
 * UUID validation schema
 */
export const UuidSchema = z.string().uuid('Invalid UUID format');

/**
 * Non-empty string validation
 */
export const NonEmptyStringSchema = z.string().min(1, 'String cannot be empty');

/**
 * Optional non-empty string validation
 */
export const OptionalNonEmptyStringSchema = z.string().min(1).optional();

/**
 * File path validation (basic)
 */
export const FilePathSchema = z.string().min(1, 'File path cannot be empty');

/**
 * Log level validation
 */
export const LogLevelSchema = z.enum(['debug', 'info', 'warn', 'error']);

/**
 * Error severity validation
 */
export const ErrorSeveritySchema = z.enum(['low', 'medium', 'high', 'critical']);

// --- Core IPC Schemas ---

/**
 * Log message payload validation
 */
export const LogMessageSchema = z.object({
  level: LogLevelSchema,
  message: NonEmptyStringSchema,
  details: z.unknown().optional(),
});

/**
 * Error reporting payload validation
 */
export const ErrorReportSchema = z.object({
  id: z.string().optional(),
  message: NonEmptyStringSchema,
  stack: z.string().optional(),
  timestamp: z.number().positive().optional(),
  source: NonEmptyStringSchema.optional(),
  severity: ErrorSeveritySchema.optional(),
  metadata: z.record(z.unknown()).optional(),
  handled: z.boolean().optional(),
});

// --- Books Extension Schemas ---

/**
 * Book ID validation - accepts both UUID and custom book ID format
 */
export const BookIdSchema = z.string().refine(
  (id) => {
    // Accept UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Accept custom book ID format: book-{timestamp}-{random}
    const bookIdRegex = /^book-\d+-[a-z0-9]+$/i;
    return uuidRegex.test(id) || bookIdRegex.test(id);
  },
  'Invalid book ID format'
);

/**
 * Chapter ID validation - accepts both UUID and custom chapter ID format
 */
export const ChapterIdSchema = z.string().refine(
  (id) => {
    // Accept UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Accept custom chapter ID format: ch-{timestamp}-{random}
    const chapterIdRegex = /^ch-\d+-[a-z0-9]+$/i;
    return uuidRegex.test(id) || chapterIdRegex.test(id);
  },
  'Invalid chapter ID format'
);

/**
 * Scene ID validation - accepts both UUID and custom scene ID format
 */
export const SceneIdSchema = z.string().refine(
  (id) => {
    // Accept UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Accept custom scene ID format: s-{timestamp}-{random} or scene-{timestamp}-{random}
    const sceneIdRegex = /^(s|scene)-\d+-[a-z0-9]+$/i;
    return uuidRegex.test(id) || sceneIdRegex.test(id);
  },
  'Invalid scene ID format'
);

/**
 * Get book structure arguments validation
 */
export const GetBookStructureArgsSchema = z.object({
  bookId: BookIdSchema,
});

/**
 * Get scene content arguments validation
 */
export const GetSceneContentArgsSchema = z.object({
  sceneId: SceneIdSchema,
});

/**
 * Save scene content arguments validation
 */
export const SaveSceneContentArgsSchema = z.object({
  sceneId: SceneIdSchema,
  content: z.string(), // Allow empty string for new scenes
});

/**
 * Autocomplete scene payload validation
 */
export const AutocompleteScenePayloadSchema = z.object({
  textBeforeCursor: z.string(),
  sceneId: SceneIdSchema.optional(),
  maxTokens: z.number().positive().max(4000).optional(),
  temperature: z.number().min(0).max(2).optional(),
});

/**
 * Token stream payload validation
 */
export const TokenStreamPayloadSchema = z.object({
  requestId: UuidSchema,
  token: z.string().optional(),
  error: z.object({
    code: NonEmptyStringSchema,
    message: NonEmptyStringSchema,
  }).optional(),
  isDone: z.boolean(),
});

// --- Characters Extension Schemas ---

/**
 * Character ID validation - accepts both UUID and custom character ID format
 */
export const CharacterIdSchema = z.string().refine(
  (id) => {
    // Accept UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Accept custom character ID format: char-{timestamp}-{random}
    const characterIdRegex = /^char-\d+-[a-z0-9]+$/i;
    return uuidRegex.test(id) || characterIdRegex.test(id);
  },
  'Invalid character ID format'
);

/**
 * Character name validation
 */
export const CharacterNameSchema = z.string().min(1).max(100, 'Character name too long');

/**
 * Character description validation
 */
export const CharacterDescriptionSchema = z.string().max(2000, 'Character description too long').optional();

/**
 * Get character profile payload validation
 */
export const GetCharacterProfilePayloadSchema = z.object({
  id: CharacterIdSchema,
});

/**
 * Create character payload validation
 */
export const CreateCharacterPayloadSchema = z.object({
  name: CharacterNameSchema,
  description: CharacterDescriptionSchema,
});

/**
 * Update character profile payload validation
 */
export const UpdateCharacterProfilePayloadSchema = z.object({
  id: CharacterIdSchema,
  data: z.object({
    name: CharacterNameSchema.optional(),
    description: CharacterDescriptionSchema,
    age: z.number().int().min(0).max(1000).optional(),
    occupation: z.string().max(100).optional(),
    personality: z.string().max(1000).optional(),
    appearance: z.string().max(1000).optional(),
    background: z.string().max(2000).optional(),
    relationships: z.array(z.object({
      characterId: CharacterIdSchema,
      relationship: z.string().max(100),
      description: z.string().max(500).optional(),
    })).optional(),
    attributes: z.array(z.object({
      name: z.string().min(1).max(50),
      value: z.string().max(200),
      type: z.enum(['trait', 'skill', 'goal', 'fear', 'motivation']).optional(),
    })).optional(),
  }),
});

/**
 * Delete character payload validation
 */
export const DeleteCharacterPayloadSchema = z.object({
  id: CharacterIdSchema,
});

/**
 * Consistency check payload validation
 */
export const ConsistencyCheckPayloadSchema = z.object({
  context: z.object({
    sceneId: SceneIdSchema,
    characterId: CharacterIdSchema,
    additionalContext: z.string().max(5000).optional(),
  }),
});

/**
 * Character event ID validation - accepts both UUID and custom event ID format
 */
export const CharacterEventIdSchema = z.string().refine(
  (id) => {
    // Accept UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Accept custom event ID format: event-{timestamp}-{random}
    const eventIdRegex = /^event-\d+-[a-z0-9]+$/i;
    return uuidRegex.test(id) || eventIdRegex.test(id);
  },
  'Invalid character event ID format'
);

/**
 * Character event validation
 */
export const CharacterEventSchema = z.object({
  id: CharacterEventIdSchema.optional(),
  characterId: CharacterIdSchema,
  type: z.enum(['trait_change', 'relationship_change', 'goal_change', 'scene_appearance']),
  description: z.string().min(1).max(1000),
  sceneId: SceneIdSchema.optional(),
  timestamp: z.number().positive().optional(),
  metadata: z.record(z.unknown()).optional(),
});

/**
 * Create character event payload validation
 */
export const CreateCharacterEventPayloadSchema = z.object({
  characterId: CharacterIdSchema,
  type: z.enum(['trait_change', 'relationship_change', 'goal_change', 'scene_appearance']),
  description: z.string().min(1).max(1000),
  sceneId: SceneIdSchema.optional(),
  metadata: z.record(z.unknown()).optional(),
});

/**
 * Update character event payload validation
 */
export const UpdateCharacterEventPayloadSchema = z.object({
  id: CharacterEventIdSchema,
  data: z.object({
    type: z.enum(['trait_change', 'relationship_change', 'goal_change', 'scene_appearance']).optional(),
    description: z.string().min(1).max(1000).optional(),
    sceneId: SceneIdSchema.optional(),
    metadata: z.record(z.unknown()).optional(),
  }),
});

// --- Window Control Schemas ---

/**
 * Window control commands (no payload needed, but we validate the channel)
 */
export const WindowControlSchema = z.object({}).optional();

// --- Export all schemas for easy access ---
export const ValidationSchemas = {
  // Common
  Uuid: UuidSchema,
  NonEmptyString: NonEmptyStringSchema,
  OptionalNonEmptyString: OptionalNonEmptyStringSchema,
  FilePath: FilePathSchema,
  LogLevel: LogLevelSchema,
  ErrorSeverity: ErrorSeveritySchema,
  
  // Core IPC
  LogMessage: LogMessageSchema,
  ErrorReport: ErrorReportSchema,
  
  // Books
  BookId: BookIdSchema,
  ChapterId: ChapterIdSchema,
  SceneId: SceneIdSchema,
  GetBookStructureArgs: GetBookStructureArgsSchema,
  GetSceneContentArgs: GetSceneContentArgsSchema,
  SaveSceneContentArgs: SaveSceneContentArgsSchema,
  AutocompleteScenePayload: AutocompleteScenePayloadSchema,
  TokenStreamPayload: TokenStreamPayloadSchema,
  
  // Characters
  CharacterId: CharacterIdSchema,
  CharacterName: CharacterNameSchema,
  CharacterDescription: CharacterDescriptionSchema,
  GetCharacterProfilePayload: GetCharacterProfilePayloadSchema,
  CreateCharacterPayload: CreateCharacterPayloadSchema,
  UpdateCharacterProfilePayload: UpdateCharacterProfilePayloadSchema,
  DeleteCharacterPayload: DeleteCharacterPayloadSchema,
  ConsistencyCheckPayload: ConsistencyCheckPayloadSchema,
  CharacterEvent: CharacterEventSchema,
  CreateCharacterEventPayload: CreateCharacterEventPayloadSchema,
  UpdateCharacterEventPayload: UpdateCharacterEventPayloadSchema,
  
  // Window Controls
  WindowControl: WindowControlSchema,
} as const;

// --- Type exports for TypeScript inference ---
export type LogMessageData = z.infer<typeof LogMessageSchema>;
export type ErrorReportData = z.infer<typeof ErrorReportSchema>;
export type GetBookStructureArgs = z.infer<typeof GetBookStructureArgsSchema>;
export type GetSceneContentArgs = z.infer<typeof GetSceneContentArgsSchema>;
export type SaveSceneContentArgs = z.infer<typeof SaveSceneContentArgsSchema>;
export type AutocompleteScenePayload = z.infer<typeof AutocompleteScenePayloadSchema>;
export type TokenStreamPayload = z.infer<typeof TokenStreamPayloadSchema>;
export type GetCharacterProfilePayload = z.infer<typeof GetCharacterProfilePayloadSchema>;
export type CreateCharacterPayload = z.infer<typeof CreateCharacterPayloadSchema>;
export type UpdateCharacterProfilePayload = z.infer<typeof UpdateCharacterProfilePayloadSchema>;
export type DeleteCharacterPayload = z.infer<typeof DeleteCharacterPayloadSchema>;
export type ConsistencyCheckPayload = z.infer<typeof ConsistencyCheckPayloadSchema>;
export type CharacterEventData = z.infer<typeof CharacterEventSchema>;
export type CreateCharacterEventPayload = z.infer<typeof CreateCharacterEventPayloadSchema>;
export type UpdateCharacterEventPayload = z.infer<typeof UpdateCharacterEventPayloadSchema>;

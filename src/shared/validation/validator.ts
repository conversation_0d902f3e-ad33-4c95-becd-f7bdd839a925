import { z } from 'zod';
import { IpcErrorData } from '@shared/types/ipc';

/**
 * Validation error class for IPC operations
 */
export class ValidationError extends Error {
  public readonly code = 'VALIDATION_ERROR';
  public readonly details: z.ZodIssue[];

  constructor(message: string, details: z.ZodIssue[]) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }

  /**
   * Convert validation error to IPC error format
   */
  toIpcError(): IpcErrorData {
    return {
      code: this.code,
      message: this.message,
      details: this.details.map(issue => ({
        path: issue.path.join('.'),
        message: issue.message,
        code: issue.code,
      })),
    };
  }
}

/**
 * Validation result type
 */
export type ValidationResult<T> = 
  | { success: true; data: T }
  | { success: false; error: ValidationError };

/**
 * Validator utility namespace for IPC operations
 */
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class Validator {
  /**
   * Validate data against a Zod schema
   */
  static validate<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult<T> {
    try {
      const result = schema.parse(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const validationError = new ValidationError(
          `Validation failed: ${error.issues.map(i => i.message).join(', ')}`,
          error.issues
        );
        return { success: false, error: validationError };
      }
      
      // Handle unexpected errors
      const validationError = new ValidationError(
        `Unexpected validation error: ${error instanceof Error ? error.message : String(error)}`,
        []
      );
      return { success: false, error: validationError };
    }
  }

  /**
   * Validate data and throw on failure (for use in handlers)
   */
  static validateOrThrow<T>(schema: z.ZodSchema<T>, data: unknown): T {
    const result = this.validate(schema, data);
    if (!result.success) {
      throw result.error;
    }
    return result.data;
  }

  /**
   * Create a validated IPC handler wrapper
   */
  static createValidatedHandler<TArgs, TResult>(
    schema: z.ZodSchema<TArgs>,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): (args: unknown) => Promise<TResult> {
    return async (args: unknown): Promise<TResult> => {
      // Validate input
      const validatedArgs = this.validateOrThrow(schema, args);
      
      // Call the actual handler with validated data
      return await handler(validatedArgs);
    };
  }

  /**
   * Create a validated IPC handler wrapper that returns IPC error format on validation failure
   */
  static createSafeValidatedHandler<TArgs, TResult>(
    schema: z.ZodSchema<TArgs>,
    handler: (args: TArgs) => Promise<TResult> | TResult
  ): (args: unknown) => Promise<TResult | IpcErrorData> {
    return async (args: unknown): Promise<TResult | IpcErrorData> => {
      try {
        // Validate input
        const validatedArgs = this.validateOrThrow(schema, args);
        
        // Call the actual handler with validated data
        return await handler(validatedArgs);
      } catch (error) {
        if (error instanceof ValidationError) {
          return error.toIpcError();
        }
        
        // Handle other errors
        return {
          code: 'HANDLER_ERROR',
          message: error instanceof Error ? error.message : String(error),
        };
      }
    };
  }

  /**
   * Validate optional data (returns undefined if data is null/undefined)
   */
  static validateOptional<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult<T | undefined> {
    if (data === null || data === undefined) {
      return { success: true, data: undefined };
    }
    return this.validate(schema, data);
  }

  /**
   * Validate array of items
   */
  static validateArray<T>(schema: z.ZodSchema<T>, data: unknown): ValidationResult<T[]> {
    if (!Array.isArray(data)) {
      const validationError = new ValidationError('Expected array', [
        {
          code: 'invalid_type',
          expected: 'array',
          received: typeof data,
          path: [],
          message: 'Expected array',
        },
      ]);
      return { success: false, error: validationError };
    }

    const results: T[] = [];
    const errors: z.ZodIssue[] = [];

    for (let i = 0; i < data.length; i++) {
      const result = this.validate(schema, data[i]);
      if (result.success) {
        results.push(result.data);
      } else {
        // Add index to error paths
        const indexedErrors = result.error.details.map(issue => ({
          ...issue,
          path: [i, ...issue.path],
        }));
        errors.push(...indexedErrors);
      }
    }

    if (errors.length > 0) {
      const validationError = new ValidationError(
        `Array validation failed: ${errors.map(e => e.message).join(', ')}`,
        errors
      );
      return { success: false, error: validationError };
    }

    return { success: true, data: results };
  }

  /**
   * Sanitize string input (trim whitespace, limit length)
   */
  static sanitizeString(input: unknown, maxLength = 10000): string {
    if (typeof input !== 'string') {
      throw new ValidationError('Expected string input', [
        {
          code: 'invalid_type',
          expected: 'string',
          received: typeof input,
          path: [],
          message: 'Expected string input',
        },
      ]);
    }

    const trimmed = input.trim();
    if (trimmed.length > maxLength) {
      throw new ValidationError(`String too long (max ${maxLength} characters)`, [
        {
          code: 'too_big',
          maximum: maxLength,
          type: 'string',
          inclusive: true,
          exact: false,
          path: [],
          message: `String too long (max ${maxLength} characters)`,
        },
      ]);
    }

    return trimmed;
  }

  /**
   * Validate and sanitize file path
   */
  static validateFilePath(input: unknown): string {
    const path = this.sanitizeString(input, 1000);
    
    // Basic path validation (prevent directory traversal)
    if (path.includes('..') || path.includes('~')) {
      throw new ValidationError('Invalid file path', [
        {
          code: 'custom',
          path: [],
          message: 'File path cannot contain ".." or "~"',
        },
      ]);
    }

    return path;
  }

  /**
   * Validate UUID format
   */
  static validateUuid(input: unknown): string {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (typeof input !== 'string') {
      throw new ValidationError('UUID must be a string', [
        {
          code: 'invalid_type',
          expected: 'string',
          received: typeof input,
          path: [],
          message: 'UUID must be a string',
        },
      ]);
    }

    if (!uuidRegex.test(input)) {
      throw new ValidationError('Invalid UUID format', [
        {
          code: 'invalid_string',
          validation: 'uuid',
          path: [],
          message: 'Invalid UUID format',
        },
      ]);
    }

    return input;
  }
}

/**
 * Decorator for automatic validation of IPC handler arguments
 */
export function ValidateArgs<T>(schema: z.ZodSchema<T>) {
  return function (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: unknown[]) {
      // Assume first argument is the data to validate
      const [data, ...restArgs] = args;
      const validatedData = Validator.validateOrThrow(schema, data);
      
      // Call original method with validated data
      return originalMethod.call(this, validatedData, ...restArgs);
    };

    return descriptor;
  };
}

/**
 * Type guard for validation results
 */
export function isValidationSuccess<T>(result: ValidationResult<T>): result is { success: true; data: T } {
  return result.success;
}

/**
 * Type guard for validation errors
 */
export function isValidationError<T>(result: ValidationResult<T>): result is { success: false; error: ValidationError } {
  return !result.success;
}

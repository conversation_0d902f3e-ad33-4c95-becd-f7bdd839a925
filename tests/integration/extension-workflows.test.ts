import { ServiceManager } from '../../src/main/service.manager';
import { ExtensionManager } from '../../src/main/extension.manager';
import { jest } from '@jest/globals';

// Mock the extension modules
jest.mock('../../src/extensions/books/main/index', () => ({
  activate: jest.fn().mockResolvedValue({
    createBook: jest.fn(),
    getBooks: jest.fn(),
    deleteBook: jest.fn(),
  }),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/extensions/characters/main/index', () => ({
  activate: jest.fn().mockResolvedValue({
    createCharacter: jest.fn(),
    getCharacters: jest.fn(),
    deleteCharacter: jest.fn(),
  }),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

describe('Extension Workflow Integration Tests', () => {
  let serviceManager: ServiceManager;
  let extensionManager: ExtensionManager;

  beforeEach(async () => {
    // Create fresh instances for each test
    serviceManager = new ServiceManager();
    extensionManager = new ExtensionManager(serviceManager.logging, serviceManager);
  });

  afterEach(async () => {
    // Clean up after each test
    await extensionManager.dispose();
    await serviceManager.dispose();
  });

  describe('Book Creation Workflow', () => {
    it('should handle complete book creation workflow', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;
      const commandService = serviceManager.commands;

      // Simulate book creation workflow
      const bookData = {
        id: 'book-test-1',
        title: 'Test Book',
        author: 'Test Author',
        description: 'A test book for integration testing'
      };

      // 1. Create book via storage
      await storageService.run(
        'INSERT INTO books (id, title, author, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        [bookData.id, bookData.title, bookData.author, bookData.description, Date.now(), Date.now()]
      );

      // 2. Verify book was created
      const createdBook = await storageService.get(
        'SELECT * FROM books WHERE id = ?',
        [bookData.id]
      );

      expect(createdBook).toBeDefined();
      expect(createdBook?.title).toBe(bookData.title);

      // 3. Create chapters for the book
      const chapterData = [
        { id: 'ch-1', title: 'Chapter 1', book_id: bookData.id },
        { id: 'ch-2', title: 'Chapter 2', book_id: bookData.id },
      ];

      for (const chapter of chapterData) {
        await storageService.run(
          'INSERT INTO chapters (id, title, book_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
          [chapter.id, chapter.title, chapter.book_id, Date.now(), Date.now()]
        );
      }

      // 4. Verify chapters were created
      const chapters = await storageService.all(
        'SELECT * FROM chapters WHERE book_id = ? ORDER BY id',
        [bookData.id]
      );

      expect(chapters).toHaveLength(2);
      expect(chapters[0].title).toBe('Chapter 1');
      expect(chapters[1].title).toBe('Chapter 2');

      // 5. Create scenes for chapters
      const sceneData = [
        { id: 's-1', title: 'Scene 1', chapter_id: 'ch-1' },
        { id: 's-2', title: 'Scene 2', chapter_id: 'ch-1' },
        { id: 's-3', title: 'Scene 3', chapter_id: 'ch-2' },
      ];

      for (const scene of sceneData) {
        await storageService.run(
          'INSERT INTO scenes (id, title, chapter_id, content, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
          [scene.id, scene.title, scene.chapter_id, '{}', Date.now(), Date.now()]
        );
      }

      // 6. Verify complete book structure
      const bookStructure = await storageService.all(`
        SELECT 
          b.id as book_id, b.title as book_title,
          c.id as chapter_id, c.title as chapter_title,
          s.id as scene_id, s.title as scene_title
        FROM books b
        LEFT JOIN chapters c ON b.id = c.book_id
        LEFT JOIN scenes s ON c.id = s.chapter_id
        WHERE b.id = ?
        ORDER BY c.id, s.id
      `, [bookData.id]);

      expect(bookStructure).toHaveLength(3); // 3 scenes total
      expect(bookStructure.every(row => row.book_title === 'Test Book')).toBe(true);
    });

    it('should handle book deletion cascade', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Create book with chapters and scenes
      const bookId = 'book-delete-test';
      await storageService.run(
        'INSERT INTO books (id, title, created_at, updated_at) VALUES (?, ?, ?, ?)',
        [bookId, 'Book to Delete', Date.now(), Date.now()]
      );

      await storageService.run(
        'INSERT INTO chapters (id, title, book_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
        ['ch-delete-1', 'Chapter 1', bookId, Date.now(), Date.now()]
      );

      await storageService.run(
        'INSERT INTO scenes (id, title, chapter_id, content, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        ['s-delete-1', 'Scene 1', 'ch-delete-1', '{}', Date.now(), Date.now()]
      );

      // Verify data exists
      const beforeBook = await storageService.get('SELECT * FROM books WHERE id = ?', [bookId]);
      const beforeChapters = await storageService.all('SELECT * FROM chapters WHERE book_id = ?', [bookId]);
      const beforeScenes = await storageService.all('SELECT * FROM scenes WHERE chapter_id = ?', ['ch-delete-1']);

      expect(beforeBook).toBeDefined();
      expect(beforeChapters).toHaveLength(1);
      expect(beforeScenes).toHaveLength(1);

      // Delete book (should cascade)
      await storageService.run('DELETE FROM books WHERE id = ?', [bookId]);

      // Verify cascade deletion
      const afterBook = await storageService.get('SELECT * FROM books WHERE id = ?', [bookId]);
      const afterChapters = await storageService.all('SELECT * FROM chapters WHERE book_id = ?', [bookId]);
      const afterScenes = await storageService.all('SELECT * FROM scenes WHERE chapter_id = ?', ['ch-delete-1']);

      expect(afterBook).toBeUndefined();
      expect(afterChapters).toHaveLength(0);
      expect(afterScenes).toHaveLength(0);
    });
  });

  describe('Character Management Workflow', () => {
    it('should handle character creation with attributes and events', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Create character
      const characterData = {
        id: 'char-test-1',
        name: 'Test Character',
        description: 'A test character',
        profile: JSON.stringify({ age: 25, occupation: 'Writer' })
      };

      await storageService.run(
        'INSERT INTO characters (id, name, description, profile, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        [characterData.id, characterData.name, characterData.description, characterData.profile, Date.now(), Date.now()]
      );

      // Add character attributes
      const attributes = [
        { id: 'attr-1', character_id: characterData.id, name: 'Strength', value: '15' },
        { id: 'attr-2', character_id: characterData.id, name: 'Intelligence', value: '18' },
      ];

      for (const attr of attributes) {
        await storageService.run(
          'INSERT INTO character_attributes (id, character_id, name, value, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
          [attr.id, attr.character_id, attr.name, attr.value, Date.now(), Date.now()]
        );
      }

      // Add character events
      const events = [
        { id: 'event-1', character_id: characterData.id, title: 'Born', description: 'Character was born' },
        { id: 'event-2', character_id: characterData.id, title: 'Graduated', description: 'Graduated from university' },
      ];

      for (const event of events) {
        await storageService.run(
          'INSERT INTO character_events (id, character_id, title, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
          [event.id, event.character_id, event.title, event.description, Date.now(), Date.now()]
        );
      }

      // Verify complete character data
      const character = await storageService.get('SELECT * FROM characters WHERE id = ?', [characterData.id]);
      const characterAttributes = await storageService.all('SELECT * FROM character_attributes WHERE character_id = ?', [characterData.id]);
      const characterEvents = await storageService.all('SELECT * FROM character_events WHERE character_id = ?', [characterData.id]);

      expect(character).toBeDefined();
      expect(character?.name).toBe('Test Character');
      expect(characterAttributes).toHaveLength(2);
      expect(characterEvents).toHaveLength(2);

      // Verify attribute values
      const strengthAttr = characterAttributes.find(attr => attr.name === 'Strength');
      const intelligenceAttr = characterAttributes.find(attr => attr.name === 'Intelligence');
      expect(strengthAttr?.value).toBe('15');
      expect(intelligenceAttr?.value).toBe('18');
    });
  });

  describe('Cross-Extension Data Relationships', () => {
    it('should handle relationships between books and characters', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Create book
      const bookId = 'book-rel-test';
      await storageService.run(
        'INSERT INTO books (id, title, created_at, updated_at) VALUES (?, ?, ?, ?)',
        [bookId, 'Book with Characters', Date.now(), Date.now()]
      );

      // Create characters
      const characters = [
        { id: 'char-rel-1', name: 'Protagonist' },
        { id: 'char-rel-2', name: 'Antagonist' },
      ];

      for (const char of characters) {
        await storageService.run(
          'INSERT INTO characters (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)',
          [char.id, char.name, Date.now(), Date.now()]
        );
      }

      // Create chapter and scene
      await storageService.run(
        'INSERT INTO chapters (id, title, book_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
        ['ch-rel-1', 'Chapter 1', bookId, Date.now(), Date.now()]
      );

      await storageService.run(
        'INSERT INTO scenes (id, title, chapter_id, content, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        ['s-rel-1', 'Scene 1', 'ch-rel-1', JSON.stringify({
          characters: ['char-rel-1', 'char-rel-2'],
          content: 'The protagonist meets the antagonist.'
        }), Date.now(), Date.now()]
      );

      // Query cross-extension data
      const sceneWithCharacters = await storageService.get(`
        SELECT 
          s.id as scene_id,
          s.title as scene_title,
          s.content,
          c.title as chapter_title,
          b.title as book_title
        FROM scenes s
        JOIN chapters c ON s.chapter_id = c.id
        JOIN books b ON c.book_id = b.id
        WHERE s.id = ?
      `, ['s-rel-1']);

      expect(sceneWithCharacters).toBeDefined();
      expect(sceneWithCharacters?.book_title).toBe('Book with Characters');
      expect(sceneWithCharacters?.chapter_title).toBe('Chapter 1');
      expect(sceneWithCharacters?.scene_title).toBe('Scene 1');

      // Verify scene content includes character references
      const sceneContent = JSON.parse(sceneWithCharacters?.content || '{}');
      expect(sceneContent.characters).toContain('char-rel-1');
      expect(sceneContent.characters).toContain('char-rel-2');
    });
  });

  describe('Extension Performance Under Load', () => {
    it('should handle bulk operations efficiently', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Measure time for bulk character creation
      const startTime = Date.now();
      const characterCount = 100;

      // Use transaction for bulk operations
      await storageService.transaction(async () => {
        for (let i = 0; i < characterCount; i++) {
          await storageService.run(
            'INSERT INTO characters (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)',
            [`bulk-char-${i}`, `Character ${i}`, Date.now(), Date.now()]
          );
        }
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Verify all characters were created
      const characters = await storageService.all('SELECT COUNT(*) as count FROM characters WHERE id LIKE ?', ['bulk-char-%']);
      expect(characters[0].count).toBe(characterCount);

      // Performance should be reasonable (less than 5 seconds for 100 characters)
      expect(duration).toBeLessThan(5000);
    });
  });
});

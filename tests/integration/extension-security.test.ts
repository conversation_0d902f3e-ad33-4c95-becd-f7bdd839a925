import { ServiceManager } from '../../src/main/service.manager';
import { ExtensionManager } from '../../src/main/extension.manager';
import { jest } from '@jest/globals';

// Mock the extension modules
jest.mock('../../src/extensions/books/main/index', () => ({
  activate: jest.fn().mockResolvedValue({
    createBook: jest.fn(),
    getBooks: jest.fn(),
  }),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/extensions/characters/main/index', () => ({
  activate: jest.fn().mockResolvedValue({
    createCharacter: jest.fn(),
    getCharacters: jest.fn(),
  }),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

describe('Extension Security and Sandboxing Tests', () => {
  let serviceManager: ServiceManager;
  let extensionManager: ExtensionManager;

  beforeEach(async () => {
    serviceManager = new ServiceManager();
    extensionManager = new ExtensionManager(serviceManager.logging, serviceManager);
  });

  afterEach(async () => {
    await extensionManager.dispose();
    await serviceManager.dispose();
  });

  describe('Extension Isolation', () => {
    it('should isolate extension IPC channels', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const ipcService = serviceManager.ipc;

      // Mock extension IPC handlers
      const booksHandler = jest.fn().mockResolvedValue('books data');
      const charactersHandler = jest.fn().mockResolvedValue('characters data');

      // Register handlers with extension prefixes
      ipcService.handle('books:getData', booksHandler);
      ipcService.handle('characters:getData', charactersHandler);

      // Verify handlers are isolated by prefix
      expect(ipcService.handle).toHaveBeenCalledWith('books:getData', booksHandler);
      expect(ipcService.handle).toHaveBeenCalledWith('characters:getData', charactersHandler);

      // Extensions should not be able to access each other's channels directly
      const booksResult = await ipcService.invoke('books:getData', {});
      const charactersResult = await ipcService.invoke('characters:getData', {});

      expect(booksResult.success).toBe(true);
      expect(charactersResult.success).toBe(true);
    });

    it('should prevent extensions from accessing core service internals', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Extensions should only have access to public APIs
      const booksApi = await extensionManager.getApi('books');
      const charactersApi = await extensionManager.getApi('characters');

      // APIs should be defined but not expose internal implementation
      expect(booksApi).toBeDefined();
      expect(charactersApi).toBeDefined();

      // Extensions should not have direct access to service manager internals
      expect(booksApi).not.toHaveProperty('serviceManager');
      expect(charactersApi).not.toHaveProperty('serviceManager');
    });

    it('should validate extension IPC input data', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Register a handler that expects specific input format
      const validationHandler = jest.fn((args: { name: string; age: number }) => {
        if (typeof args.name !== 'string' || typeof args.age !== 'number') {
          throw new Error('Invalid input format');
        }
        return { success: true };
      });

      ipcService.handle('test:validateInput', validationHandler);

      // Valid input should work
      const validResult = await ipcService.invoke('test:validateInput', { name: 'John', age: 25 });
      expect(validResult.success).toBe(true);

      // Invalid input should be handled gracefully
      const invalidResult = await ipcService.invoke('test:validateInput', { name: 123, age: 'invalid' });
      expect(invalidResult.success).toBe(false);
      expect(invalidResult.error).toBeDefined();
    });
  });

  describe('Resource Access Control', () => {
    it('should limit extension database access to their own tables', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Extensions should be able to access their own tables
      await expect(
        storageService.run('INSERT INTO books (id, title) VALUES (?, ?)', ['test-book', 'Test Book'])
      ).resolves.not.toThrow();

      await expect(
        storageService.run('INSERT INTO characters (id, name) VALUES (?, ?)', ['test-char', 'Test Character'])
      ).resolves.not.toThrow();

      // Verify data was inserted correctly
      const book = await storageService.get('SELECT * FROM books WHERE id = ?', ['test-book']);
      const character = await storageService.get('SELECT * FROM characters WHERE id = ?', ['test-char']);

      expect(book).toBeDefined();
      expect(character).toBeDefined();
    });

    it('should prevent SQL injection in extension queries', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const storageService = serviceManager.storage;

      // Create test data
      await storageService.run('INSERT INTO books (id, title) VALUES (?, ?)', ['safe-book', 'Safe Book']);

      // Attempt SQL injection (should be prevented by parameterized queries)
      const maliciousInput = "'; DROP TABLE books; --";
      
      // This should not affect the database due to parameterized queries
      const result = await storageService.get('SELECT * FROM books WHERE title = ?', [maliciousInput]);
      expect(result).toBeUndefined(); // No match found, which is expected

      // Verify the books table still exists and contains data
      const books = await storageService.all('SELECT * FROM books');
      expect(books.length).toBeGreaterThan(0);
      expect(books.some(book => book.id === 'safe-book')).toBe(true);
    });
  });

  describe('Extension Error Boundaries', () => {
    it('should contain extension errors and not crash the system', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Register handlers that throw different types of errors
      const errorHandlers = [
        { channel: 'test:typeError', error: new TypeError('Type error') },
        { channel: 'test:referenceError', error: new ReferenceError('Reference error') },
        { channel: 'test:customError', error: new Error('Custom error') },
      ];

      errorHandlers.forEach(({ channel, error }) => {
        ipcService.handle(channel, () => {
          throw error;
        });
      });

      // All error handlers should be contained and return error responses
      for (const { channel } of errorHandlers) {
        const result = await ipcService.invoke(channel, {});
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      }

      // System should still be functional after errors
      const healthCheck = jest.fn().mockResolvedValue('healthy');
      ipcService.handle('test:healthCheck', healthCheck);
      
      const healthResult = await ipcService.invoke('test:healthCheck', {});
      expect(healthResult.success).toBe(true);
    });

    it('should handle memory leaks in extension operations', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const storageService = serviceManager.storage;

      // Simulate memory-intensive operations
      const largeDataOperations = [];
      for (let i = 0; i < 10; i++) {
        const operation = storageService.run(
          'INSERT INTO books (id, title, description) VALUES (?, ?, ?)',
          [`memory-test-${i}`, `Book ${i}`, 'A'.repeat(1000)] // Large description
        );
        largeDataOperations.push(operation);
      }

      // All operations should complete without memory issues
      await expect(Promise.all(largeDataOperations)).resolves.not.toThrow();

      // Verify data was stored correctly
      const books = await storageService.all('SELECT COUNT(*) as count FROM books WHERE id LIKE ?', ['memory-test-%']);
      expect(books[0].count).toBe(10);

      // Cleanup large data
      await storageService.run('DELETE FROM books WHERE id LIKE ?', ['memory-test-%']);
    });
  });

  describe('Extension Communication Security', () => {
    it('should validate cross-extension API calls', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Get extension APIs
      const booksApi = await extensionManager.getApi('books');
      const charactersApi = await extensionManager.getApi('characters');

      expect(booksApi).toBeDefined();
      expect(charactersApi).toBeDefined();

      // APIs should be objects with defined methods
      expect(typeof booksApi).toBe('object');
      expect(typeof charactersApi).toBe('object');

      // APIs should not expose sensitive internal methods
      expect(booksApi).not.toHaveProperty('dispose');
      expect(charactersApi).not.toHaveProperty('dispose');
    });

    it('should rate limit extension IPC calls', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Register a handler that tracks call frequency
      let callCount = 0;
      const rateLimitedHandler = jest.fn(() => {
        callCount++;
        return { callNumber: callCount };
      });

      ipcService.handle('test:rateLimited', rateLimitedHandler);

      // Make multiple rapid calls
      const rapidCalls = [];
      for (let i = 0; i < 10; i++) {
        rapidCalls.push(ipcService.invoke('test:rateLimited', {}));
      }

      const results = await Promise.all(rapidCalls);

      // All calls should complete (basic rate limiting would be implemented at the IPC level)
      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
      });

      expect(callCount).toBe(10);
    });
  });

  describe('Extension Lifecycle Security', () => {
    it('should clean up extension resources on deactivation', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Verify extensions are active
      const booksApi = await extensionManager.getApi('books');
      const charactersApi = await extensionManager.getApi('characters');
      expect(booksApi).toBeDefined();
      expect(charactersApi).toBeDefined();

      // Deactivate extensions
      await extensionManager.deactivateAll();

      // APIs should no longer be available
      const booksApiAfter = await extensionManager.getApi('books');
      const charactersApiAfter = await extensionManager.getApi('characters');
      expect(booksApiAfter).toBeUndefined();
      expect(charactersApiAfter).toBeUndefined();
    });

    it('should prevent extension reactivation without proper cleanup', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();

      // Activate, deactivate, then reactivate
      await extensionManager.activateAll();
      await extensionManager.deactivateAll();
      await extensionManager.activateAll();

      // Extensions should be properly reactivated
      const booksApi = await extensionManager.getApi('books');
      const charactersApi = await extensionManager.getApi('characters');
      expect(booksApi).toBeDefined();
      expect(charactersApi).toBeDefined();

      // Verify activation was called the correct number of times
      const { activate: activateBooks } = await import('../../src/extensions/books/main/index');
      const { activate: activateCharacters } = await import('../../src/extensions/characters/main/index');
      
      expect(activateBooks).toHaveBeenCalledTimes(2); // Once for each activation
      expect(activateCharacters).toHaveBeenCalledTimes(2);
    });
  });
});

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ServiceManager } from '../../src/main/service.manager';
import { ExtensionManager } from '../../src/main/extension.manager';
import type { Command } from '../../src/shared/types/commands';
import type { ViewDescriptor } from '../../src/main/services/view.service';

// Mock file system operations for extension discovery
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  mkdirSync: jest.fn(),
  readdirSync: jest.fn(() => ['characters', 'books', 'workbench']),
  statSync: jest.fn(() => ({ isDirectory: () => true })),
}));

jest.mock('path', () => ({
  join: jest.fn((...args) => args.join('/')),
  dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
  resolve: jest.fn((...args) => args.join('/')),
}));

// Mock dynamic imports for extensions
jest.mock('../../src/extensions/characters/main/index', () => ({
  activate: jest.fn().mockResolvedValue(undefined),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/extensions/books/main/index', () => ({
  activate: jest.fn().mockResolvedValue(undefined),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../../src/extensions/workbench/main/index', () => ({
  activate: jest.fn().mockResolvedValue(undefined),
  deactivate: jest.fn().mockResolvedValue(undefined),
}));

describe('Extension System Integration', () => {
  let serviceManager: ServiceManager;
  let extensionManager: ExtensionManager;

  beforeEach(() => {
    serviceManager = new ServiceManager();
    extensionManager = new ExtensionManager(serviceManager, () => jest.fn());
  });

  describe('Service and Extension Lifecycle', () => {
    it('should initialize services and then activate extensions', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      // Initialize services first
      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Verify core services are available
      const coreAPI = serviceManager.getCoreServicesAPI();
      expect(coreAPI.commands).toBeDefined();
      expect(coreAPI.views).toBeDefined();
      expect(coreAPI.storage).toBeDefined();

      // Discover and activate extensions
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Verify extensions were activated
      const { activate: activateCharacters } = await import('../../src/extensions/characters/main/index');
      const { activate: activateBooks } = await import('../../src/extensions/books/main/index');
      const { activate: activateWorkbench } = await import('../../src/extensions/workbench/main/index');

      expect(activateCharacters).toHaveBeenCalled();
      expect(activateBooks).toHaveBeenCalled();
      expect(activateWorkbench).toHaveBeenCalled();
    });

    it('should provide extension context with core services', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Check that extensions received proper context
      const { activate: activateCharacters } = await import('../../src/extensions/characters/main/index');
      
      expect(activateCharacters).toHaveBeenCalledWith(
        expect.objectContaining({
          commands: expect.any(Object),
          views: expect.any(Object),
          storage: expect.any(Object),
          subscriptions: expect.any(Array),
        })
      );
    });
  });

  describe('Command Registration and Execution', () => {
    it('should allow extensions to register and execute commands', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const commandService = serviceManager.commands;
      
      // Simulate extension registering a command
      const mockHandler = jest.fn().mockResolvedValue('command result');
      const testCommand: Command = {
        id: 'test.extension.command',
        title: 'Test Extension Command',
        category: 'Test',
        handler: mockHandler,
        when: 'always',
      };

      const disposable = commandService.registerCommand(testCommand);

      // Execute the command
      const result = await commandService.executeCommand({
        commandId: 'test.extension.command',
        args: { param: 'value' },
      });

      expect(result).toBe('command result');
      expect(mockHandler).toHaveBeenCalledWith({ param: 'value' });

      // Clean up
      await disposable.dispose();
    });

    it('should handle command registration from multiple extensions', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const commandService = serviceManager.commands;

      // Register commands from different extensions
      const command1: Command = {
        id: 'characters.create',
        title: 'Create Character',
        category: 'Characters',
        handler: jest.fn(),
        when: 'always',
      };

      const command2: Command = {
        id: 'books.create',
        title: 'Create Book',
        category: 'Books',
        handler: jest.fn(),
        when: 'always',
      };

      const disposable1 = commandService.registerCommand(command1);
      const disposable2 = commandService.registerCommand(command2);

      // Verify both commands are registered
      const commands = commandService.getCommands();
      const commandIds = commands.map(cmd => cmd.id);
      
      expect(commandIds).toContain('characters.create');
      expect(commandIds).toContain('books.create');

      // Clean up
      await disposable1.dispose();
      await disposable2.dispose();
    });
  });

  describe('View Registration and Management', () => {
    it('should allow extensions to register views', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const viewService = serviceManager.views;

      // Simulate extension registering views
      const characterView: ViewDescriptor = {
        id: 'characters.list',
        name: 'Characters',
        componentName: 'CharacterListView',
        extensionId: 'characters',
        location: 'sidebar',
        icon: 'users',
      };

      const bookView: ViewDescriptor = {
        id: 'books.explorer',
        name: 'Books',
        componentName: 'BookExplorerView',
        extensionId: 'books',
        location: 'sidebar',
        icon: 'book',
      };

      const disposable1 = viewService.registerView(characterView);
      const disposable2 = viewService.registerView(bookView);

      // Verify views are registered
      const views = viewService.getRegisteredViews();
      const viewIds = views.map(view => view.id);

      expect(viewIds).toContain('characters.list');
      expect(viewIds).toContain('books.explorer');

      // Clean up
      await disposable1.dispose();
      await disposable2.dispose();
    });
  });

  describe('Storage Service Integration', () => {
    it('should provide storage service to extensions', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const storageService = serviceManager.storage;

      // Verify storage service is initialized and available
      expect(storageService).toBeDefined();
      expect(typeof storageService.run).toBe('function');
      expect(typeof storageService.get).toBe('function');
      expect(typeof storageService.all).toBe('function');
      expect(typeof storageService.transaction).toBe('function');
    });

    it('should allow extensions to perform database operations', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const storageService = serviceManager.storage;

      // Mock successful database operations
      const mockRunResult = { changes: 1, lastInsertRowid: 1 };
      storageService.run = jest.fn().mockResolvedValue(mockRunResult);
      storageService.get = jest.fn().mockResolvedValue({ id: 1, name: 'test' });
      storageService.all = jest.fn().mockResolvedValue([{ id: 1, name: 'test' }]);

      // Simulate extension using storage
      const runResult = await storageService.run('INSERT INTO test (name) VALUES (?)', 'test');
      const getResult = await storageService.get('SELECT * FROM test WHERE id = ?', 1);
      const allResult = await storageService.all('SELECT * FROM test');

      expect(runResult).toEqual(mockRunResult);
      expect(getResult).toEqual({ id: 1, name: 'test' });
      expect(allResult).toEqual([{ id: 1, name: 'test' }]);
    });
  });

  describe('Extension Communication', () => {
    it('should allow extensions to communicate through events', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Simulate extension registering IPC handlers
      const mockHandler = jest.fn().mockResolvedValue('handler result');
      ipcService.handle('test.extension.action', mockHandler);

      // Verify handler was registered
      expect(ipcService.handle).toHaveBeenCalledWith('test.extension.action', mockHandler);
    });

    it('should handle cross-extension communication', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Test that one extension can call another extension's API
      const charactersApi = await extensionManager.getApi('characters');
      const booksApi = await extensionManager.getApi('books');

      expect(charactersApi).toBeDefined();
      expect(booksApi).toBeDefined();
    });

    it('should isolate extension IPC channels', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Simulate two extensions registering handlers with same local channel name
      const handler1 = jest.fn().mockResolvedValue('extension1 result');
      const handler2 = jest.fn().mockResolvedValue('extension2 result');

      // These should be prefixed differently
      ipcService.handle('extension1:getData', handler1);
      ipcService.handle('extension2:getData', handler2);

      // Verify both handlers were registered with different prefixed channels
      expect(ipcService.handle).toHaveBeenCalledWith('extension1:getData', handler1);
      expect(ipcService.handle).toHaveBeenCalledWith('extension2:getData', handler2);
    });
  });

  describe('Extension Deactivation', () => {
    it('should properly deactivate extensions and clean up resources', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Deactivate extensions
      await extensionManager.deactivateAll();

      // Verify deactivation was called
      const { deactivate: deactivateCharacters } = await import('../../src/extensions/characters/main/index');
      const { deactivate: deactivateBooks } = await import('../../src/extensions/books/main/index');
      const { deactivate: deactivateWorkbench } = await import('../../src/extensions/workbench/main/index');

      expect(deactivateCharacters).toHaveBeenCalled();
      expect(deactivateBooks).toHaveBeenCalled();
      expect(deactivateWorkbench).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle extension activation errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Mock one extension to fail activation
      const { activate: activateCharacters } = await import('../../src/extensions/characters/main/index');
      (activateCharacters as jest.Mock).mockRejectedValueOnce(new Error('Activation failed'));

      await extensionManager.discoverExtensions();

      // Should not throw, but should handle the error
      await expect(extensionManager.activateAll()).resolves.not.toThrow();
    });

    it('should continue working when one service fails', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      // Mock one service to fail initialization
      const originalStorageService = serviceManager.storage;
      Object.defineProperty(serviceManager, 'storage', {
        get: () => {
          throw new Error('Storage service failed');
        },
        configurable: true,
      });

      // Other services should still work
      await expect(serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent)).rejects.toThrow();

      // Restore storage service
      Object.defineProperty(serviceManager, 'storage', {
        get: () => originalStorageService,
        configurable: true,
      });
    });
  });

  describe('Extension Data Persistence', () => {
    it('should allow extensions to persist and retrieve data', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      const storageService = serviceManager.storage;

      // Test that extensions can store and retrieve data
      const testData = { name: 'Test Character', age: 25 };
      await storageService.run(
        'INSERT INTO characters (id, name, data) VALUES (?, ?, ?)',
        ['test-char-1', 'Test Character', JSON.stringify(testData)]
      );

      const result = await storageService.get(
        'SELECT * FROM characters WHERE id = ?',
        ['test-char-1']
      );

      expect(result).toBeDefined();
      expect(result?.name).toBe('Test Character');
    });

    it('should handle concurrent extension data operations', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const storageService = serviceManager.storage;

      // Simulate concurrent operations from different extensions
      const operations = [
        storageService.run('INSERT INTO books (id, title) VALUES (?, ?)', ['book1', 'Book 1']),
        storageService.run('INSERT INTO characters (id, name) VALUES (?, ?)', ['char1', 'Character 1']),
        storageService.run('INSERT INTO books (id, title) VALUES (?, ?)', ['book2', 'Book 2']),
        storageService.run('INSERT INTO characters (id, name) VALUES (?, ?)', ['char2', 'Character 2']),
      ];

      // All operations should complete successfully
      await expect(Promise.all(operations)).resolves.not.toThrow();

      // Verify data was stored correctly
      const books = await storageService.all('SELECT * FROM books ORDER BY id');
      const characters = await storageService.all('SELECT * FROM characters ORDER BY id');

      expect(books).toHaveLength(2);
      expect(characters).toHaveLength(2);
    });
  });

  describe('Extension Error Recovery', () => {
    it('should continue functioning when one extension fails', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      // Mock one extension to fail during activation
      const { activate: activateCharacters } = await import('../../src/extensions/characters/main/index');
      (activateCharacters as jest.Mock).mockRejectedValueOnce(new Error('Characters extension failed'));

      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Books extension should still work
      const booksApi = await extensionManager.getApi('books');
      expect(booksApi).toBeDefined();

      // Characters extension should not be available
      const charactersApi = await extensionManager.getApi('characters');
      expect(charactersApi).toBeUndefined();
    });

    it('should handle extension IPC errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);

      const ipcService = serviceManager.ipc;

      // Register a handler that throws an error
      const errorHandler = jest.fn().mockRejectedValue(new Error('IPC handler error'));
      ipcService.handle('test.error.handler', errorHandler);

      // The error should be caught and handled gracefully
      const result = await ipcService.invoke('test.error.handler', {});
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Extension Lifecycle Edge Cases', () => {
    it('should handle rapid activation/deactivation cycles', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();

      // Rapid activation/deactivation cycles
      for (let i = 0; i < 3; i++) {
        await extensionManager.activateAll();
        await extensionManager.deactivateAll();
      }

      // Final activation should still work
      await extensionManager.activateAll();
      const booksApi = await extensionManager.getApi('books');
      expect(booksApi).toBeDefined();
    });

    it('should prevent double activation of extensions', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();

      // Activate twice
      await extensionManager.activateAll();
      await extensionManager.activateAll();

      // Extension should only be activated once
      const { activate: activateBooks } = await import('../../src/extensions/books/main/index');
      expect(activateBooks).toHaveBeenCalledTimes(1);
    });
  });

  describe('System Disposal', () => {
    it('should properly dispose all services and extensions', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Dispose everything
      await extensionManager.dispose();
      await serviceManager.dispose();

      // Verify disposal was called
      expect(serviceManager.serviceRegistry.dispose).toBeDefined();
    });

    it('should handle disposal errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const mockSendRendererEvent = jest.fn();

      await serviceManager.initializeServices(mockWindowGetter, mockSendRendererEvent);
      await extensionManager.discoverExtensions();
      await extensionManager.activateAll();

      // Mock one extension to fail during deactivation
      const { deactivate: deactivateBooks } = await import('../../src/extensions/books/main/index');
      if (deactivateBooks) {
        (deactivateBooks as jest.Mock).mockRejectedValueOnce(new Error('Deactivation failed'));
      }

      // Disposal should not throw even if one extension fails
      await expect(extensionManager.dispose()).resolves.not.toThrow();
      await expect(serviceManager.dispose()).resolves.not.toThrow();
    });
  });
});

// Mock for electron-store
export default class ElectronStore {
  private store: Map<string, any> = new Map();

  constructor(options?: any) {
    // Mock constructor
  }

  get(key: string, defaultValue?: any): any {
    return this.store.get(key) ?? defaultValue;
  }

  set(key: string, value: any): void {
    this.store.set(key, value);
  }

  has(key: string): boolean {
    return this.store.has(key);
  }

  delete(key: string): void {
    this.store.delete(key);
  }

  clear(): void {
    this.store.clear();
  }

  get size(): number {
    return this.store.size;
  }

  get store(): Record<string, any> {
    const obj: Record<string, any> = {};
    for (const [key, value] of this.store.entries()) {
      obj[key] = value;
    }
    return obj;
  }

  set store(value: Record<string, any>) {
    this.store.clear();
    for (const [key, val] of Object.entries(value)) {
      this.store.set(key, val);
    }
  }

  onDidChange(key: string, callback: (newValue: any, oldValue: any) => void): () => void {
    // Mock change listener
    return () => {}; // Return unsubscribe function
  }

  onDidAnyChange(callback: (newValue: any, oldValue: any) => void): () => void {
    // Mock change listener for any key
    return () => {}; // Return unsubscribe function
  }

  openInEditor(): void {
    // Mock editor opening
  }

  get path(): string {
    return '/mock/path/to/config.json';
  }
}

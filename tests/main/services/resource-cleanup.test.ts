import { ResourceMonitorService } from '../../../src/main/services/resource-monitor.service';
import { DisposalVerificationUtility } from '../../../src/main/utils/disposal-verification';
import { LoggingService } from '../../../src/main/services/logging.service';
import { StorageService } from '../../../src/main/services/storage.service';
import { ContextService } from '../../../src/main/services/context.service';
import { KeybindingService } from '../../../src/main/services/keybinding.service';
import { ConfigurationService } from '../../../src/main/services/configuration.service';
import { HealthService } from '../../../src/main/services/health.service';
import { LifecycleService } from '../../../src/main/services/lifecycle.service';
import { IpcService } from '../../../src/main/services/ipc.service';

describe('Resource Cleanup and Disposal Patterns', () => {
  let mockLogger: jest.Mocked<LoggingService>;
  let resourceMonitor: ResourceMonitorService;
  let disposalVerifier: DisposalVerificationUtility;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn().mockReturnThis(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as any;

    resourceMonitor = new ResourceMonitorService(mockLogger);
    disposalVerifier = new DisposalVerificationUtility(mockLogger);
  });

  afterEach(async () => {
    await resourceMonitor.dispose();
  });

  describe('ResourceMonitorService', () => {
    it('should start and stop monitoring', () => {
      resourceMonitor.startMonitoring();
      expect(mockLogger.info).toHaveBeenCalledWith('Resource monitoring started');

      resourceMonitor.stopMonitoring();
      expect(mockLogger.info).toHaveBeenCalledWith('Resource monitoring stopped');
    });

    it('should get current resource usage', () => {
      const usage = resourceMonitor.getCurrentResourceUsage();
      
      expect(usage).toHaveProperty('memoryUsage');
      expect(usage).toHaveProperty('cpuUsage');
      expect(usage).toHaveProperty('timestamp');
      expect(usage).toHaveProperty('activeHandles');
      expect(usage).toHaveProperty('activeRequests');
      expect(typeof usage.timestamp).toBe('number');
    });

    it('should force garbage collection when available', () => {
      // Mock global.gc
      const originalGc = global.gc;
      global.gc = jest.fn();

      resourceMonitor.forceGarbageCollection();
      expect(global.gc).toHaveBeenCalled();

      // Restore original
      global.gc = originalGc;
    });

    it('should handle missing garbage collection gracefully', () => {
      const originalGc = global.gc;
      delete (global as any).gc;

      resourceMonitor.forceGarbageCollection();
      expect(mockLogger.debug).toHaveBeenCalledWith('Garbage collection not available (run with --expose-gc)');

      // Restore original
      global.gc = originalGc;
    });

    it('should dispose properly', async () => {
      resourceMonitor.startMonitoring();
      await resourceMonitor.dispose();

      expect(mockLogger.info).toHaveBeenCalledWith('ResourceMonitorService disposed successfully');
    });
  });

  describe('Service Disposal Verification', () => {
    let mockIpcService: jest.Mocked<IpcService>;

    beforeEach(() => {
      mockIpcService = {
        handle: jest.fn(),
        removeHandler: jest.fn(),
        send: jest.fn(),
        dispose: jest.fn(),
      } as any;
    });

    it('should verify ContextService disposal', async () => {
      const contextService = new ContextService(mockLogger, mockIpcService);
      
      const result = await disposalVerifier.verifyServiceDisposal(contextService, 'ContextService');
      
      expect(result.serviceName).toBe('ContextService');
      expect(result.criticalIssues).toHaveLength(0);
      // Service should have dispose method and no critical issues
      expect(typeof contextService.dispose).toBe('function');
    });

    it('should verify KeybindingService disposal', async () => {
      const mockContextService = {
        evaluate: jest.fn(() => true),
      } as any;
      
      const keybindingService = new KeybindingService(mockLogger, mockContextService, mockIpcService);
      
      const result = await disposalVerifier.verifyServiceDisposal(keybindingService, 'KeybindingService');
      
      expect(result.serviceName).toBe('KeybindingService');
      expect(result.criticalIssues).toHaveLength(0);
      expect(typeof keybindingService.dispose).toBe('function');
    });

    it('should verify ConfigurationService disposal', async () => {
      const configService = new ConfigurationService(mockLogger, mockIpcService);
      
      const result = await disposalVerifier.verifyServiceDisposal(configService, 'ConfigurationService');
      
      expect(result.serviceName).toBe('ConfigurationService');
      expect(result.criticalIssues).toHaveLength(0);
      expect(typeof configService.dispose).toBe('function');
    });

    it('should verify HealthService disposal', async () => {
      const healthService = new HealthService(mockLogger);
      
      const result = await disposalVerifier.verifyServiceDisposal(healthService, 'HealthService');
      
      expect(result.serviceName).toBe('HealthService');
      expect(result.criticalIssues).toHaveLength(0);
      expect(typeof healthService.dispose).toBe('function');
    });

    it('should verify LifecycleService disposal', async () => {
      const lifecycleService = new LifecycleService(mockLogger);
      
      const result = await disposalVerifier.verifyServiceDisposal(lifecycleService, 'LifecycleService');
      
      expect(result.serviceName).toBe('LifecycleService');
      expect(result.criticalIssues).toHaveLength(0);
      expect(typeof lifecycleService.dispose).toBe('function');
    });

    it('should detect services without dispose method', async () => {
      const badService = {
        // Missing dispose method
      } as any;
      
      const result = await disposalVerifier.verifyServiceDisposal(badService, 'BadService');
      
      expect(result.serviceName).toBe('BadService');
      expect(result.isProperlyDisposed).toBe(false);
      expect(result.overallScore).toBe(0);
      expect(result.criticalIssues).toContain('Service does not implement dispose() method');
    });

    it('should generate comprehensive disposal report', async () => {
      const contextService = new ContextService(mockLogger, mockIpcService);
      const healthService = new HealthService(mockLogger);
      
      const results = [
        await disposalVerifier.verifyServiceDisposal(contextService, 'ContextService'),
        await disposalVerifier.verifyServiceDisposal(healthService, 'HealthService'),
      ];
      
      const report = disposalVerifier.generateReport(results);
      
      expect(report).toContain('# Service Disposal Verification Report');
      expect(report).toContain('## Summary');
      expect(report).toContain('## ContextService');
      expect(report).toContain('## HealthService');
      expect(report).toContain('Total Services: 2');
    });
  });

  describe('Resource Leak Detection', () => {
    it('should detect memory leaks in service disposal', async () => {
      // Create a service that intentionally leaks memory
      const leakyService = {
        dispose: async () => {
          // Intentionally create a large object that won't be cleaned up
          (global as any).leakyData = new Array(1000000).fill('leak');
        }
      } as any;

      const result = await resourceMonitor.testServiceDisposal('LeakyService', leakyService);
      
      expect(result.serviceName).toBe('LeakyService');
      // Note: Memory leak detection may not always be reliable in test environment
      expect(result).toHaveProperty('memoryLeakMB');
      expect(result).toHaveProperty('handleLeak');
      
      // Cleanup
      delete (global as any).leakyData;
    });

    it('should detect proper resource cleanup', async () => {
      const cleanService = {
        dispose: async () => {
          // Proper cleanup - no leaks
        }
      } as any;

      const result = await resourceMonitor.testServiceDisposal('CleanService', cleanService);
      
      expect(result.serviceName).toBe('CleanService');
      expect(result.memoryLeakMB).toBeLessThan(1); // Should be minimal
    });
  });

  describe('Resource Usage Monitoring', () => {
    it('should record resource usage history', () => {
      resourceMonitor.startMonitoring();
      
      // Wait a bit for monitoring to record some data
      return new Promise(resolve => {
        setTimeout(() => {
          resourceMonitor.stopMonitoring();
          const history = resourceMonitor.getResourceHistory();
          
          expect(Array.isArray(history)).toBe(true);
          // History might be empty in fast test environment
          resolve(undefined);
        }, 100);
      });
    });

    it('should limit resource history size', () => {
      const history = resourceMonitor.getResourceHistory();
      expect(history.length).toBeLessThanOrEqual(100); // maxHistorySize
    });
  });

  describe('Integration Tests', () => {
    let mockIpcServiceForIntegration: jest.Mocked<IpcService>;

    beforeEach(() => {
      mockIpcServiceForIntegration = {
        handle: jest.fn(),
        removeHandler: jest.fn(),
        send: jest.fn(),
        dispose: jest.fn(),
      } as any;
    });

    it('should verify all core services have proper disposal', async () => {
      const services = [
        { name: 'ContextService', instance: new ContextService(mockLogger, mockIpcServiceForIntegration) },
        { name: 'HealthService', instance: new HealthService(mockLogger) },
        { name: 'LifecycleService', instance: new LifecycleService(mockLogger) },
      ];

      const results = [];
      for (const service of services) {
        const result = await disposalVerifier.verifyServiceDisposal(service.instance, service.name);
        results.push(result);
      }

      // All services should have dispose methods
      const allHaveDispose = services.every(s => typeof s.instance.dispose === 'function');
      expect(allHaveDispose).toBe(true);

      // No critical issues (missing dispose method)
      const totalCriticalIssues = results.reduce((sum, r) => sum + r.criticalIssues.length, 0);
      expect(totalCriticalIssues).toBe(0);

      // All services should have some resource cleanup checks
      const allHaveChecks = results.every(r => r.resourceCleanupResults.length >= 0);
      expect(allHaveChecks).toBe(true);
    });
  });
});

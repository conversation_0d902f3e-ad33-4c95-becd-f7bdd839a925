import 'reflect-metadata';
import { DependencyInjectionContainer, ServiceLifecycle } from '../../../src/main/services/dependency-injection.service';
import { ServiceContainerBuilder } from '../../../src/main/services/service-container-builder';
import { createServiceToken, ServiceTokens } from '../../../src/main/services/service-tokens';
import { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import { jest } from '@jest/globals';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createScopedLogger: jest.fn().mockReturnThis(),
} as unknown as LoggingServiceAPI;

// Test services
interface ITestService {
  getName(): string;
  initialize?(): Promise<void>;
  dispose?(): Promise<void>;
}

class TestService implements ITestService {
  constructor(private name: string = 'TestService') {}
  
  getName(): string {
    return this.name;
  }
  
  async initialize(): Promise<void> {
    // Mock initialization
  }
  
  async dispose(): Promise<void> {
    // Mock disposal
  }
}

class DependentService implements ITestService {
  constructor(private dependency: ITestService) {}
  
  getName(): string {
    return `DependentService(${this.dependency.getName()})`;
  }
}

// Service tokens
const TestServiceToken = createServiceToken<ITestService>('test-service');
const DependentServiceToken = createServiceToken<ITestService>('dependent-service');
const AnotherServiceToken = createServiceToken<ITestService>('another-service');

describe('DependencyInjectionContainer', () => {
  let container: DependencyInjectionContainer;

  beforeEach(() => {
    jest.clearAllMocks();
    container = new DependencyInjectionContainer(mockLogger);
  });

  afterEach(async () => {
    await container.dispose();
  });

  describe('Service Registration', () => {
    it('should register a service with factory', () => {
      container.register(
        TestServiceToken,
        () => new TestService('FactoryService'),
        [],
        { lifecycle: 'singleton' }
      );

      expect(container.isRegistered(TestServiceToken)).toBe(true);
    });

    it('should register a service with constructor', () => {
      container.register(
        TestServiceToken,
        TestService,
        [],
        { lifecycle: 'singleton' }
      );

      expect(container.isRegistered(TestServiceToken)).toBe(true);
    });

    it('should register singleton service', () => {
      container.registerSingleton(TestServiceToken, TestService);
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.options.lifecycle).toBe('singleton');
    });

    it('should register transient service', () => {
      container.registerTransient(TestServiceToken, TestService);
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.options.lifecycle).toBe('transient');
    });

    it('should register instance', () => {
      const instance = new TestService('InstanceService');
      container.registerInstance(TestServiceToken, instance);
      
      expect(container.isRegistered(TestServiceToken)).toBe(true);
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.instance).toBe(instance);
    });
  });

  describe('Service Resolution', () => {
    it('should resolve singleton service', async () => {
      container.registerSingleton(TestServiceToken, TestService);
      
      const instance1 = await container.resolve(TestServiceToken);
      const instance2 = await container.resolve(TestServiceToken);
      
      expect(instance1).toBe(instance2); // Same instance
      expect(instance1.getName()).toBe('TestService');
    });

    it('should resolve transient service', async () => {
      container.registerTransient(TestServiceToken, TestService);
      
      const instance1 = await container.resolve(TestServiceToken);
      const instance2 = await container.resolve(TestServiceToken);
      
      expect(instance1).not.toBe(instance2); // Different instances
      expect(instance1.getName()).toBe('TestService');
      expect(instance2.getName()).toBe('TestService');
    });

    it('should resolve service with dependencies', async () => {
      container.registerSingleton(TestServiceToken, TestService);
      container.registerSingleton(
        DependentServiceToken,
        async (container) => new DependentService(await container.resolve(TestServiceToken)),
        [TestServiceToken]
      );

      const instance = await container.resolve(DependentServiceToken);
      expect(instance.getName()).toBe('DependentService(TestService)');
    });

    it('should resolve service using factory', async () => {
      container.register(
        TestServiceToken,
        () => new TestService('FactoryCreated'),
        [],
        { lifecycle: 'singleton' }
      );
      
      const instance = await container.resolve(TestServiceToken);
      expect(instance.getName()).toBe('FactoryCreated');
    });

    it('should throw error for unregistered service', async () => {
      await expect(container.resolve(TestServiceToken)).rejects.toThrow('Service not registered');
    });

    it('should detect circular dependencies', async () => {
      // Create circular dependency: A -> B -> A
      container.register(
        TestServiceToken,
        async () => await container.resolve(DependentServiceToken),
        [DependentServiceToken]
      );

      container.register(
        DependentServiceToken,
        async () => await container.resolve(TestServiceToken),
        [TestServiceToken]
      );

      await expect(container.resolve(TestServiceToken)).rejects.toThrow('Circular dependency detected');
    });
  });

  describe('Service Lifecycle', () => {
    it('should track service lifecycle states', async () => {
      container.registerSingleton(TestServiceToken, TestService);
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.lifecycle).toBe(ServiceLifecycle.REGISTERED);
      
      await container.resolve(TestServiceToken);
      expect(registration?.lifecycle).toBe(ServiceLifecycle.INITIALIZED);
    });

    it('should initialize services that have initialize method', async () => {
      const initializeSpy = jest.spyOn(TestService.prototype, 'initialize');
      container.registerSingleton(TestServiceToken, TestService);
      
      await container.resolve(TestServiceToken);
      expect(initializeSpy).toHaveBeenCalled();
    });

    it('should handle service creation errors', async () => {
      container.register(
        TestServiceToken,
        () => { throw new Error('Creation failed'); },
        []
      );
      
      await expect(container.resolve(TestServiceToken)).rejects.toThrow('Failed to create service');
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.lifecycle).toBe(ServiceLifecycle.ERROR);
    });
  });

  describe('Scoped Services', () => {
    it('should create and manage scopes', async () => {
      container.register(TestServiceToken, TestService, [], { lifecycle: 'scoped' });
      
      container.createScope('scope1');
      container.enterScope('scope1');
      
      const instance1 = await container.resolve(TestServiceToken);
      const instance2 = await container.resolve(TestServiceToken);
      
      expect(instance1).toBe(instance2); // Same instance within scope
      
      container.exitScope();
    });

    it('should create different instances in different scopes', async () => {
      container.register(TestServiceToken, TestService, [], { lifecycle: 'scoped' });
      
      container.createScope('scope1');
      container.enterScope('scope1');
      const instance1 = await container.resolve(TestServiceToken);
      container.exitScope();
      
      container.createScope('scope2');
      container.enterScope('scope2');
      const instance2 = await container.resolve(TestServiceToken);
      container.exitScope();
      
      expect(instance1).not.toBe(instance2); // Different instances in different scopes
    });

    it('should dispose scoped services when scope is disposed', async () => {
      const disposeSpy = jest.spyOn(TestService.prototype, 'dispose');
      container.register(TestServiceToken, TestService, [], { lifecycle: 'scoped' });
      
      container.createScope('scope1');
      container.enterScope('scope1');
      await container.resolve(TestServiceToken);
      container.exitScope();
      
      await container.disposeScope('scope1');
      expect(disposeSpy).toHaveBeenCalled();
    });
  });

  describe('Service Information', () => {
    it('should get all registrations', () => {
      container.registerSingleton(TestServiceToken, TestService);
      container.registerTransient(DependentServiceToken, DependentService, [TestServiceToken]);
      
      const registrations = container.getAllRegistrations();
      expect(registrations).toHaveLength(2);
    });

    it('should get services by tag', () => {
      container.register(TestServiceToken, TestService, [], { tags: ['test', 'core'] });
      container.register(DependentServiceToken, DependentService, [], { tags: ['test'] });
      container.register(AnotherServiceToken, TestService, [], { tags: ['other'] });
      
      const testServices = container.getServicesByTag('test');
      const coreServices = container.getServicesByTag('core');
      
      expect(testServices).toHaveLength(2);
      expect(coreServices).toHaveLength(1);
    });

    it('should track last accessed time', async () => {
      container.registerSingleton(TestServiceToken, TestService);
      
      const beforeResolve = Date.now();
      await container.resolve(TestServiceToken);
      const afterResolve = Date.now();
      
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.lastAccessed).toBeGreaterThanOrEqual(beforeResolve);
      expect(registration?.lastAccessed).toBeLessThanOrEqual(afterResolve);
    });
  });

  describe('Container Disposal', () => {
    it('should dispose all services when container is disposed', async () => {
      const disposeSpy = jest.spyOn(TestService.prototype, 'dispose');
      container.registerSingleton(TestServiceToken, TestService);
      
      await container.resolve(TestServiceToken); // Create instance
      await container.dispose();
      
      expect(disposeSpy).toHaveBeenCalled();
    });

    it('should throw error when accessing disposed container', async () => {
      await container.dispose();
      
      expect(() => container.isRegistered(TestServiceToken)).toThrow('Container has been disposed');
    });
  });
});

describe('ServiceContainerBuilder', () => {
  let builder: ServiceContainerBuilder;

  beforeEach(() => {
    jest.clearAllMocks();
    builder = new ServiceContainerBuilder(mockLogger);
  });

  afterEach(async () => {
    const container = builder.build();
    await container.dispose();
  });

  describe('Fluent Registration', () => {
    it('should register services fluently', () => {
      builder
        .registerSingleton(TestServiceToken, TestService)
        .registerTransient(DependentServiceToken, DependentService, [TestServiceToken]);
      
      const container = builder.build();
      expect(container.isRegistered(TestServiceToken)).toBe(true);
      expect(container.isRegistered(DependentServiceToken)).toBe(true);
    });

    it('should register instances', () => {
      const instance = new TestService('BuilderInstance');
      builder.registerInstance(TestServiceToken, instance);
      
      const container = builder.build();
      const registration = container.getRegistration(TestServiceToken);
      expect(registration?.instance).toBe(instance);
    });
  });

  describe('Validation', () => {
    it('should validate successful configuration', () => {
      builder
        .registerSingleton(TestServiceToken, TestService)
        .registerSingleton(DependentServiceToken, DependentService, [TestServiceToken]);
      
      expect(() => builder.validate()).not.toThrow();
    });

    it('should detect missing dependencies', () => {
      builder.registerSingleton(DependentServiceToken, DependentService, [TestServiceToken]);
      // TestServiceToken is not registered

      let caughtError: any;
      try {
        builder.build();
      } catch (error: any) {
        caughtError = error;
      }

      expect(caughtError).toBeDefined();
      expect(caughtError.message).toMatch(/Unregistered dependency.*test-service.*dependent-service/);
    });
  });

  describe('Statistics', () => {
    it('should provide container statistics', () => {
      builder
        .registerSingleton(TestServiceToken, TestService)
        .registerTransient(DependentServiceToken, DependentService, [TestServiceToken]);
      
      const stats = builder.getStatistics();
      expect(stats.totalServices).toBe(2);
      expect(stats.singletonServices).toBe(1);
      expect(stats.transientServices).toBe(1);
    });
  });

  describe('Configuration Export', () => {
    it('should export configuration for debugging', () => {
      builder
        .registerSingleton(TestServiceToken, TestService)
        .registerTransient(DependentServiceToken, DependentService, [TestServiceToken]);
      
      const config = builder.exportConfiguration();
      expect(config.services).toHaveLength(2);
      expect(config.services[0].lifecycle).toBe('singleton');
      expect(config.services[1].lifecycle).toBe('transient');
    });
  });
});

import { HealthService, HealthStatus, I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HealthCheckResult } from '../../../src/main/services/health.service';
import { BaseService } from '../../../src/main/services/base.service';
import { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import { jest } from '@jest/globals';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createScopedLogger: jest.fn().mockReturnThis(),
} as unknown as LoggingServiceAPI;

// Mock service
class MockService extends BaseService {
  public isDisposed = false;

  constructor() {
    super(mockLogger, 'MockService');
  }

  async initialize(): Promise<void> {
    this.isInitialized = true;
  }

  async dispose(): Promise<void> {
    this.isDisposed = true;
    await super.dispose();
  }
}

// Mock health checker
class MockHealthChecker implements IHealthChecker {
  private shouldFail = false;
  private responseTime = 100;

  setFailure(shouldFail: boolean): void {
    this.shouldFail = shouldFail;
  }

  setResponseTime(ms: number): void {
    this.responseTime = ms;
  }

  async checkHealth(): Promise<HealthCheckResult> {
    await new Promise(resolve => setTimeout(resolve, this.responseTime));

    if (this.shouldFail) {
      throw new Error('Mock health check failure');
    }

    return {
      status: HealthStatus.HEALTHY,
      message: 'Mock service is healthy',
      timestamp: Date.now(),
      responseTime: this.responseTime,
    };
  }
}

describe('HealthService', () => {
  let healthService: HealthService;
  let mockService: MockService;
  let mockHealthChecker: MockHealthChecker;

  beforeEach(async () => {
    jest.clearAllMocks();
    healthService = new HealthService(mockLogger);
    await healthService.initialize(() => null);
    
    mockService = new MockService();
    await mockService.initialize(() => null);
    
    mockHealthChecker = new MockHealthChecker();
  });

  afterEach(async () => {
    await healthService.dispose();
    await mockService.dispose();
  });

  describe('Service Registration', () => {
    it('should register a service for health monitoring', () => {
      healthService.registerService('test-service', mockService, mockHealthChecker);

      const health = healthService.getServiceHealth('test-service');
      expect(health).toBeDefined();
      expect(health?.serviceName).toBe('test-service');
      expect(health?.status).toBe(HealthStatus.UNKNOWN);
    });

    it('should register a service without health checker', () => {
      healthService.registerService('test-service', mockService);

      const health = healthService.getServiceHealth('test-service');
      expect(health).toBeDefined();
      expect(health?.serviceName).toBe('test-service');
    });

    it('should register multiple services', () => {
      const service2 = new MockService();
      
      healthService.registerService('service1', mockService);
      healthService.registerService('service2', service2);

      const allHealth = healthService.getAllServiceHealth();
      expect(allHealth.size).toBe(2);
      expect(allHealth.has('service1')).toBe(true);
      expect(allHealth.has('service2')).toBe(true);
    });
  });

  describe('Health Monitoring', () => {
    beforeEach(() => {
      healthService.registerService('test-service', mockService, mockHealthChecker, {
        interval: 100, // Fast interval for testing
        timeout: 1000,
        retryAttempts: 2,
        restartThreshold: 3,
        enabled: true,
      });
    });

    it('should start and stop monitoring', () => {
      healthService.startMonitoring();
      // Monitoring should be running
      
      healthService.stopMonitoring();
      // Monitoring should be stopped
    });

    it('should perform health checks', async () => {
      const result = await healthService.checkServiceHealth('test-service');
      
      expect(result).toBeDefined();
      expect(result?.status).toBe(HealthStatus.HEALTHY);
      expect(result?.message).toBe('Mock service is healthy');
    });

    it('should handle health check failures', async () => {
      mockHealthChecker.setFailure(true);
      
      const result = await healthService.checkServiceHealth('test-service');
      
      expect(result).toBeDefined();
      expect(result?.status).toBe(HealthStatus.UNHEALTHY);
      expect(result?.message).toContain('Mock health check failure');
    });

    it('should update service health after checks', async () => {
      await healthService.checkServiceHealth('test-service');
      
      const health = healthService.getServiceHealth('test-service');
      expect(health?.status).toBe(HealthStatus.HEALTHY);
      expect(health?.totalChecks).toBe(1);
      expect(health?.totalFailures).toBe(0);
    });

    it('should track consecutive failures', async () => {
      mockHealthChecker.setFailure(true);
      
      // Perform multiple failed checks
      await healthService.checkServiceHealth('test-service');
      await healthService.checkServiceHealth('test-service');
      
      const health = healthService.getServiceHealth('test-service');
      expect(health?.consecutiveFailures).toBe(2);
      expect(health?.totalFailures).toBe(2);
    });

    it('should reset consecutive failures on recovery', async () => {
      // Fail first
      mockHealthChecker.setFailure(true);
      await healthService.checkServiceHealth('test-service');
      
      // Then recover
      mockHealthChecker.setFailure(false);
      await healthService.checkServiceHealth('test-service');
      
      const health = healthService.getServiceHealth('test-service');
      expect(health?.consecutiveFailures).toBe(0);
      expect(health?.status).toBe(HealthStatus.HEALTHY);
    });
  });

  describe('Default Health Checks', () => {
    beforeEach(() => {
      // Register service without custom health checker
      healthService.registerService('default-service', mockService);
    });

    it('should perform default health check for healthy service', async () => {
      const result = await healthService.checkServiceHealth('default-service');
      
      expect(result).toBeDefined();
      expect(result?.status).toBe(HealthStatus.HEALTHY);
      expect(result?.message).toBe('Service is responsive');
    });

    it('should detect disposed service', async () => {
      mockService.isDisposed = true;
      
      const result = await healthService.checkServiceHealth('default-service');
      
      expect(result).toBeDefined();
      expect(result?.status).toBe(HealthStatus.UNHEALTHY);
      expect(result?.message).toBe('Service is disposed');
    });
  });

  describe('Global Health Calculation', () => {
    beforeEach(() => {
      const service1 = new MockService();
      const service2 = new MockService();
      const service3 = new MockService();
      
      healthService.registerService('healthy', service1);
      healthService.registerService('degraded', service2);
      healthService.registerService('unhealthy', service3);
    });

    it('should calculate global health score', async () => {
      // Set different health statuses
      const health1 = healthService.getServiceHealth('healthy');
      const health2 = healthService.getServiceHealth('degraded');
      const health3 = healthService.getServiceHealth('unhealthy');

      if (health1) health1.status = HealthStatus.HEALTHY;
      if (health2) health2.status = HealthStatus.DEGRADED;
      if (health3) health3.status = HealthStatus.UNHEALTHY;

      // Trigger global health calculation
      (healthService as any).updateGlobalHealth();

      const score = healthService.getGlobalHealthScore();
      // (1.0 + 0.7 + 0.3) / 3 = 0.67
      expect(score).toBeCloseTo(0.67, 2);
    });

    it('should determine system health status', async () => {
      // All healthy
      const health1 = healthService.getServiceHealth('healthy');
      const health2 = healthService.getServiceHealth('degraded');
      const health3 = healthService.getServiceHealth('unhealthy');

      if (health1) health1.status = HealthStatus.HEALTHY;
      if (health2) health2.status = HealthStatus.HEALTHY;
      if (health3) health3.status = HealthStatus.HEALTHY;

      (healthService as any).updateGlobalHealth();
      expect(healthService.getSystemHealthStatus()).toBe(HealthStatus.HEALTHY);

      // Mixed health - need more degraded services to get below 0.9 threshold
      if (health2) health2.status = HealthStatus.DEGRADED;
      if (health3) health3.status = HealthStatus.DEGRADED;
      (healthService as any).updateGlobalHealth();
      expect(healthService.getSystemHealthStatus()).toBe(HealthStatus.DEGRADED);

      // Critical health
      if (health1) health1.status = HealthStatus.CRITICAL;
      if (health2) health2.status = HealthStatus.CRITICAL;
      if (health3) health3.status = HealthStatus.CRITICAL;
      (healthService as any).updateGlobalHealth();
      expect(healthService.getSystemHealthStatus()).toBe(HealthStatus.CRITICAL);
    });
  });

  describe('Service Recovery', () => {
    it('should attempt service restart on threshold failures', async () => {
      const mockFactory = jest.fn(() => new MockService());
      
      healthService.registerService('restart-test', mockService, mockHealthChecker, {
        interval: 100,
        timeout: 1000,
        retryAttempts: 1,
        restartThreshold: 2,
        enabled: true,
      }, mockFactory);
      
      // Simulate failures to trigger restart
      mockHealthChecker.setFailure(true);
      
      // This would normally trigger restart in the monitoring loop
      // For testing, we need to simulate the failure handling
      const health = healthService.getServiceHealth('restart-test');
      if (health) {
        health.consecutiveFailures = 3; // Exceed threshold
      }
      
      expect(mockFactory).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent service health check', async () => {
      const result = await healthService.checkServiceHealth('non-existent');
      expect(result).toBeNull();
    });

    it('should handle health check timeouts', async () => {
      mockHealthChecker.setResponseTime(2000); // Longer than timeout
      
      healthService.registerService('timeout-test', mockService, mockHealthChecker, {
        interval: 100,
        timeout: 500, // Short timeout
        retryAttempts: 1,
        restartThreshold: 3,
        enabled: true,
      });
      
      const result = await healthService.checkServiceHealth('timeout-test');
      expect(result?.status).toBe(HealthStatus.UNHEALTHY);
      expect(result?.message).toContain('timeout');
    });
  });

  describe('Disposal', () => {
    it('should stop monitoring on disposal', async () => {
      healthService.registerService('test-service', mockService);
      healthService.startMonitoring();
      
      await healthService.dispose();
      
      // Monitoring should be stopped
      expect(healthService.isDisposed).toBe(true);
    });

    it('should throw error when accessing disposed service', () => {
      healthService.dispose();
      
      expect(() => {
        healthService.getSystemHealthStatus();
      }).toThrow();
    });
  });
});

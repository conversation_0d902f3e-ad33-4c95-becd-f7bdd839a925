import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { StorageService } from '../../../src/main/services/storage.service';
import type { LoggingServiceAPI } from '../../../src/main/services/logging.service';
import type { IpcServiceAPI } from '../../../src/main/services/ipc.service';
import Database from 'better-sqlite3';

// Mock the dependencies
jest.mock('better-sqlite3');
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(() => '/tmp/test-app-data'),
  },
}));
jest.mock('fs', () => ({
  existsSync: jest.fn(() => true),
  mkdirSync: jest.fn(),
}));

describe('StorageService', () => {
  let storageService: StorageService;
  let mockLogger: jest.Mocked<LoggingServiceAPI>;
  let mockIpcService: jest.Mocked<IpcServiceAPI>;
  let mockDatabase: jest.Mocked<Database>;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createScopedLogger: jest.fn(() => mockLogger),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<LoggingServiceAPI>;

    // Create mock IPC service
    mockIpcService = {
      handle: jest.fn(),
      send: jest.fn(),
      initialize: jest.fn(),
      dispose: jest.fn(),
    } as jest.Mocked<IpcServiceAPI>;

    // Create mock database
    mockDatabase = {
      prepare: jest.fn(),
      pragma: jest.fn(),
      close: jest.fn(),
      exec: jest.fn(),
      transaction: jest.fn(),
    } as unknown as jest.Mocked<Database>;

    // Mock Database constructor to return our mock
    (Database as jest.MockedClass<typeof Database>).mockImplementation(() => mockDatabase);

    // Mock Database constructor
    (Database as jest.MockedClass<typeof Database>).mockImplementation(() => mockDatabase);

    storageService = new StorageService(mockLogger, mockIpcService);
  });

  describe('Initialization', () => {
    it('should initialize database connection successfully', async () => {
      const mockWindowGetter = jest.fn(() => null);

      await storageService.initialize(mockWindowGetter);

      expect(Database).toHaveBeenCalledWith(expect.stringContaining('ai_books_data.sqlite'));
      expect(mockDatabase.pragma).toHaveBeenCalledWith('journal_mode = WAL');
      expect(mockDatabase.pragma).toHaveBeenCalledWith('foreign_keys = ON');
      expect(mockLogger.info).toHaveBeenCalledWith('Database connection pool initialized successfully.');
    });

    it('should handle database initialization errors', async () => {
      const mockWindowGetter = jest.fn(() => null);
      const error = new Error('Database connection failed');
      (Database as jest.MockedClass<typeof Database>).mockImplementation(() => {
        throw error;
      });

      await expect(storageService.initialize(mockWindowGetter)).rejects.toThrow(
        'Failed to initialize database: Database connection failed'
      );

      expect(mockLogger.error).toHaveBeenCalledWith('Failed to initialize database', error);
    });
  });

  describe('Database Operations', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);
    });

    describe('run', () => {
      it('should execute SQL statement successfully', async () => {
        const mockStatement = {
          run: jest.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.run(
          'INSERT INTO test (name) VALUES (?)',
          'test-name'
        );

        expect(mockDatabase.prepare).toHaveBeenCalledWith('INSERT INTO test (name) VALUES (?)');
        expect(mockStatement.run).toHaveBeenCalledWith('test-name');
        expect(result).toEqual({ changes: 1, lastInsertRowid: 1 });
      });

      it('should handle SQL execution errors', async () => {
        const error = new Error('SQL error');
        const mockStatement = {
          run: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.run('INVALID SQL', 'param')
        ).rejects.toThrow('Failed to run statement');

        expect(mockLogger.error).toHaveBeenCalledWith(
          'RUN Error: INVALID SQL',
          expect.objectContaining({ params: ['param'], error })
        );
      });
    });

    describe('get', () => {
      it('should retrieve single row successfully', async () => {
        const mockRow = { id: 1, name: 'test' };
        const mockStatement = {
          get: jest.fn(() => mockRow),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.get('SELECT * FROM test WHERE id = ?', 1);

        expect(mockDatabase.prepare).toHaveBeenCalledWith('SELECT * FROM test WHERE id = ?');
        expect(mockStatement.get).toHaveBeenCalledWith(1);
        expect(result).toEqual(mockRow);
      });

      it('should return undefined when no row found', async () => {
        const mockStatement = {
          get: jest.fn(() => undefined),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.get('SELECT * FROM test WHERE id = ?', 999);

        expect(result).toBeUndefined();
      });

      it('should handle query errors', async () => {
        const error = new Error('Query error');
        const mockStatement = {
          get: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.get('INVALID SQL', 'param')
        ).rejects.toThrow('Failed to get row');
      });
    });

    describe('all', () => {
      it('should retrieve all rows successfully', async () => {
        const mockRows = [
          { id: 1, name: 'test1' },
          { id: 2, name: 'test2' },
        ];
        const mockStatement = {
          all: jest.fn(() => mockRows),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.all('SELECT * FROM test');

        expect(mockDatabase.prepare).toHaveBeenCalledWith('SELECT * FROM test');
        expect(mockStatement.all).toHaveBeenCalledWith();
        expect(result).toEqual(mockRows);
      });

      it('should return empty array when no rows found', async () => {
        const mockStatement = {
          all: jest.fn(() => []),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        const result = await storageService.all('SELECT * FROM empty_table');

        expect(result).toEqual([]);
      });

      it('should handle query errors', async () => {
        const error = new Error('Query error');
        const mockStatement = {
          all: jest.fn(() => {
            throw error;
          }),
        };
        mockDatabase.prepare.mockReturnValue(mockStatement as any);

        await expect(
          storageService.all('INVALID SQL')
        ).rejects.toThrow('Failed to get all rows');
      });
    });

    describe('transaction', () => {
      it('should execute transaction successfully', async () => {
        const callback = jest.fn(() => 'result');
        const mockTransactionFunction = jest.fn(() => 'result');
        const mockTransaction = jest.fn(() => mockTransactionFunction);
        mockDatabase.transaction = mockTransaction;

        const result = await storageService.transaction(callback);

        expect(mockTransaction).toHaveBeenCalledWith(callback);
        expect(mockTransactionFunction).toHaveBeenCalled();
        expect(result).toBe('result');
      });

      it('should handle transaction errors', async () => {
        const error = new Error('Transaction error');
        const callback = jest.fn();
        const mockTransactionFunction = jest.fn(() => {
          throw error;
        });
        const mockTransaction = jest.fn(() => mockTransactionFunction);
        mockDatabase.transaction = mockTransaction;

        await expect(
          storageService.transaction(callback)
        ).rejects.toThrow('Transaction failed');
      });
    });
  });

  describe('Performance Optimization', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);
    });

    it('should create performance indexes during initialization', async () => {
      // Verify that exec was called for index creation
      expect(mockDatabase.exec).toHaveBeenCalledWith(
        expect.stringContaining('CREATE INDEX IF NOT EXISTS')
      );

      // Check that multiple indexes were created
      const execCalls = mockDatabase.exec.mock.calls;
      const indexCalls = execCalls.filter(call =>
        call[0].includes('CREATE INDEX IF NOT EXISTS')
      );

      expect(indexCalls.length).toBeGreaterThan(10); // Should create multiple indexes
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('Successfully created')
      );
    });

    it('should handle index creation errors gracefully', async () => {
      // Reset mocks and make exec throw for some indexes
      jest.clearAllMocks();
      mockDatabase.exec.mockImplementation((sql: string) => {
        if (sql.includes('idx_books_created_at')) {
          throw new Error('Index already exists');
        }
      });

      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      // Should log warning but not fail initialization
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Failed to create index'),
        expect.any(Error)
      );
    });

    it('should analyze query performance', async () => {
      const mockQueryPlan = [
        { detail: 'SCAN TABLE books' },
        { detail: 'USE TEMP B-TREE FOR ORDER BY' }
      ];

      const mockPrepareExplain = {
        all: jest.fn(() => mockQueryPlan)
      };
      const mockPrepareData = {
        all: jest.fn(() => [])
      };

      mockDatabase.prepare
        .mockReturnValueOnce(mockPrepareExplain as any)
        .mockReturnValueOnce(mockPrepareData as any);

      const result = await storageService.analyzeQueryPerformance(
        'SELECT * FROM books ORDER BY createdAt'
      );

      expect(result.queryPlan).toEqual(mockQueryPlan);
      expect(result.executionTime).toBeGreaterThanOrEqual(0);
      expect(result.recommendations.some(rec =>
        rec.includes('Consider adding an index for table scan')
      )).toBe(true);
      expect(result.recommendations.some(rec =>
        rec.includes('Consider adding an index to avoid sorting')
      )).toBe(true);
    });

    it('should get database statistics', async () => {
      const mockTables = [{ name: 'books' }, { name: 'chapters' }];
      const mockIndexes = [
        { name: 'idx_books_created_at', tbl_name: 'books', sql: 'CREATE INDEX idx_books_created_at ON books(createdAt DESC)' }
      ];

      // Mock the prepared statements that will be cached
      const mockStatements = {
        tables: { all: jest.fn(() => mockTables) },
        rowCount1: { get: jest.fn(() => ({ count: 10 })) },
        tableInfo1: { all: jest.fn(() => []) },
        pageCount1: { get: jest.fn(() => ({ page_count: 100 })) },
        rowCount2: { get: jest.fn(() => ({ count: 5 })) },
        tableInfo2: { all: jest.fn(() => []) },
        pageCount2: { get: jest.fn(() => ({ page_count: 50 })) },
        indexes: { all: jest.fn(() => mockIndexes) }
      };

      // Set up prepare mock to return different statements for different queries
      mockDatabase.prepare.mockImplementation((sql: string) => {
        if (sql.includes('sqlite_master') && sql.includes("type='table'")) {
          return mockStatements.tables;
        } else if (sql.includes('COUNT(*)') && sql.includes('books')) {
          return mockStatements.rowCount1;
        } else if (sql.includes('table_info') && sql.includes('books')) {
          return mockStatements.tableInfo1;
        } else if (sql.includes('page_count')) {
          return mockStatements.pageCount1;
        } else if (sql.includes('COUNT(*)') && sql.includes('chapters')) {
          return mockStatements.rowCount2;
        } else if (sql.includes('table_info') && sql.includes('chapters')) {
          return mockStatements.tableInfo2;
        } else if (sql.includes('sqlite_master') && sql.includes("type='index'")) {
          return mockStatements.indexes;
        }
        return { all: jest.fn(() => []), get: jest.fn(() => ({})) };
      });

      mockDatabase.pragma
        .mockReturnValueOnce('wal')
        .mockReturnValueOnce(1)
        .mockReturnValueOnce(2000)
        .mockReturnValueOnce(4096);

      const result = await storageService.getDatabaseStatistics();

      expect(result.tableStats).toHaveLength(2);
      expect(result.tableStats[0].tableName).toBe('books');
      expect(result.tableStats[0].rowCount).toBe(10);
      expect(result.indexStats).toHaveLength(1);
      expect(result.indexStats[0].indexName).toBe('idx_books_created_at');
      expect(result.pragmaInfo.journalMode).toBe('wal');
      expect(result.pragmaInfo.foreignKeys).toBe(true);
    });
  });

  describe('Connection Pool & Statement Caching', () => {
    beforeEach(async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);
    });

    it('should get pool statistics', async () => {
      const stats = await storageService.getPoolStatistics();

      expect(stats).toHaveProperty('totalConnections');
      expect(stats).toHaveProperty('activeConnections');
      expect(stats).toHaveProperty('idleConnections');
      expect(stats).toHaveProperty('statementCacheHits');
      expect(stats).toHaveProperty('statementCacheMisses');
      expect(stats).toHaveProperty('statementCacheSize');
      expect(stats).toHaveProperty('averageAcquireTime');

      expect(stats.totalConnections).toBeGreaterThan(0);
    });

    it('should clear statement cache', async () => {
      await storageService.clearStatementCache();

      const stats = await storageService.getPoolStatistics();
      expect(stats.statementCacheSize).toBe(0);
      expect(mockLogger.info).toHaveBeenCalledWith('Prepared statement cache cleared');
    });

    it('should reuse prepared statements from cache', async () => {
      const mockStatement = {
        run: jest.fn(() => ({ changes: 1, lastInsertRowid: 1 })),
      };
      mockDatabase.prepare.mockReturnValue(mockStatement as any);

      // Execute same query twice
      await storageService.run('INSERT INTO test (name) VALUES (?)', 'test1');
      await storageService.run('INSERT INTO test (name) VALUES (?)', 'test2');

      // Should prepare statement only once due to caching
      expect(mockDatabase.prepare).toHaveBeenCalledTimes(1);
      expect(mockStatement.run).toHaveBeenCalledTimes(2);
    });

    it('should handle concurrent operations with connection pool', async () => {
      const mockStatement = {
        get: jest.fn(() => ({ id: 1, name: 'test' })),
      };
      mockDatabase.prepare.mockReturnValue(mockStatement as any);

      // Execute multiple concurrent operations
      const promises = Array.from({ length: 3 }, (_, i) =>
        storageService.get('SELECT * FROM test WHERE id = ?', i + 1)
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      expect(mockStatement.get).toHaveBeenCalledTimes(3);
    });
  });

  describe('Disposal', () => {
    it('should close all database connections in pool on disposal', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      await storageService.dispose();

      expect(mockDatabase.pragma).toHaveBeenCalledWith('wal_checkpoint(RESTART)');
      expect(mockDatabase.close).toHaveBeenCalled();
      expect(mockLogger.info).toHaveBeenCalledWith('Database connection pool disposed.');
    });

    it('should handle WAL checkpoint errors gracefully', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      const checkpointError = new Error('Checkpoint failed');
      mockDatabase.pragma.mockImplementation((pragma) => {
        if (pragma === 'wal_checkpoint(RESTART)') {
          throw checkpointError;
        }
      });

      await storageService.dispose();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error during WAL checkpoint for connection'),
        checkpointError
      );
      expect(mockDatabase.close).toHaveBeenCalled();
    });

    it('should handle database close errors', async () => {
      const mockWindowGetter = jest.fn(() => null);
      await storageService.initialize(mockWindowGetter);

      const closeError = new Error('Close failed');
      mockDatabase.close.mockImplementation(() => {
        throw closeError;
      });

      await storageService.dispose();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error closing database connection'),
        closeError
      );
    });
  });

  describe('Error Handling', () => {
    it('should throw error when database connection fails', async () => {
      // Create a new service instance that hasn't been initialized
      const newStorageService = new StorageService(mockLogger, mockIpcService);

      // Make Database constructor throw an error
      (Database as jest.MockedClass<typeof Database>).mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      await expect(
        newStorageService.run('SELECT 1')
      ).rejects.toThrow('Database connection failed');
    });
  });
});

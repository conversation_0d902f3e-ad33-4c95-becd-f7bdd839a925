import { GitService } from '../../../src/main/services/git.service';
import { LoggingService } from '../../../src/main/services/logging.service';
import { IpcService } from '../../../src/main/services/ipc.service';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

describe('GitService', () => {
  let gitService: GitService;
  let logger: LoggingService;
  let ipcService: IpcService;
  let tempDir: string;

  beforeEach(async () => {
    // Create temporary directory for testing
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'git-service-test-'));
    
    // Create mock services
    logger = new LoggingService();
    await logger.initialize();
    
    ipcService = new IpcService(logger);
    await ipcService.initialize(() => null);
    
    gitService = new GitService(logger, ipcService);
    await gitService.initialize();
  });

  afterEach(async () => {
    // Cleanup
    await gitService.dispose();
    await ipcService.dispose();
    await logger.dispose();
    
    // Remove temporary directory
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('Failed to cleanup temp directory:', error);
    }
  });

  describe('initRepo', () => {
    it('should initialize a new Git repository', async () => {
      await gitService.initRepo(tempDir);
      
      // Check if .git directory was created
      const gitDir = path.join(tempDir, '.git');
      const stats = await fs.stat(gitDir);
      expect(stats.isDirectory()).toBe(true);
      
      // Check if .gitignore was created
      const gitignorePath = path.join(tempDir, '.gitignore');
      const gitignoreExists = await fs.access(gitignorePath).then(() => true).catch(() => false);
      expect(gitignoreExists).toBe(true);
    });

    it('should not fail when repository already exists', async () => {
      // Initialize twice
      await gitService.initRepo(tempDir);
      await expect(gitService.initRepo(tempDir)).resolves.not.toThrow();
    });

    it('should create directory if it does not exist', async () => {
      const newDir = path.join(tempDir, 'new-repo');
      await gitService.initRepo(newDir);
      
      const stats = await fs.stat(newDir);
      expect(stats.isDirectory()).toBe(true);
    });
  });

  describe('status', () => {
    beforeEach(async () => {
      await gitService.initRepo(tempDir);
    });

    it('should return repository status', async () => {
      const status = await gitService.status(tempDir);
      
      expect(status).toHaveProperty('branch');
      expect(status).toHaveProperty('ahead');
      expect(status).toHaveProperty('behind');
      expect(status).toHaveProperty('staged');
      expect(status).toHaveProperty('unstaged');
      expect(status).toHaveProperty('untracked');
      
      expect(Array.isArray(status.staged)).toBe(true);
      expect(Array.isArray(status.unstaged)).toBe(true);
      expect(Array.isArray(status.untracked)).toBe(true);
    });

    it('should throw error for non-git directory', async () => {
      const nonGitDir = path.join(tempDir, 'not-git');
      await fs.mkdir(nonGitDir);
      
      await expect(gitService.status(nonGitDir)).rejects.toThrow('Not a Git repository');
    });
  });

  describe('stage and commit', () => {
    beforeEach(async () => {
      await gitService.initRepo(tempDir);
    });

    it('should stage and commit files', async () => {
      // Create a test file
      const testFile = path.join(tempDir, 'test.txt');
      await fs.writeFile(testFile, 'Hello, World!');
      
      // Stage the file
      await gitService.stage(tempDir, 'test.txt');
      
      // Check status
      const statusAfterStage = await gitService.status(tempDir);
      expect(statusAfterStage.staged.length).toBeGreaterThan(0);
      
      // Commit the file
      await gitService.commit(tempDir, 'Initial commit');
      
      // Check status after commit
      const statusAfterCommit = await gitService.status(tempDir);
      expect(statusAfterCommit.staged.length).toBe(0);
    });

    it('should handle multiple files', async () => {
      // Create multiple test files
      const files = ['file1.txt', 'file2.txt', 'file3.txt'];
      for (const file of files) {
        await fs.writeFile(path.join(tempDir, file), `Content of ${file}`);
      }
      
      // Stage all files
      await gitService.stage(tempDir, files);
      
      // Check status
      const status = await gitService.status(tempDir);
      expect(status.staged.length).toBe(files.length);
    });

    it('should not commit when no staged changes', async () => {
      // Try to commit without staging anything
      await expect(gitService.commit(tempDir, 'Empty commit')).resolves.not.toThrow();
    });
  });

  describe('log', () => {
    beforeEach(async () => {
      await gitService.initRepo(tempDir);
      
      // Create and commit a test file to have some history
      const testFile = path.join(tempDir, 'test.txt');
      await fs.writeFile(testFile, 'Initial content');
      await gitService.stage(tempDir, 'test.txt');
      await gitService.commit(tempDir, 'Initial commit');
    });

    it('should return commit history', async () => {
      const commits = await gitService.log(tempDir);
      
      expect(Array.isArray(commits)).toBe(true);
      expect(commits.length).toBeGreaterThan(0);
      
      const firstCommit = commits[0];
      expect(firstCommit).toHaveProperty('hash');
      expect(firstCommit).toHaveProperty('shortHash');
      expect(firstCommit).toHaveProperty('message');
      expect(firstCommit).toHaveProperty('author');
      expect(firstCommit).toHaveProperty('email');
      expect(firstCommit).toHaveProperty('date');
      
      expect(firstCommit.message).toBe('Initial commit');
    });
  });

  describe('remove', () => {
    beforeEach(async () => {
      await gitService.initRepo(tempDir);
    });

    it('should remove files from Git index', async () => {
      // Create and stage a file
      const testFile = path.join(tempDir, 'to-remove.txt');
      await fs.writeFile(testFile, 'This will be removed');
      await gitService.stage(tempDir, 'to-remove.txt');
      await gitService.commit(tempDir, 'Add file to remove');
      
      // Remove the file from Git
      await gitService.remove(tempDir, 'to-remove.txt');
      
      // Check status
      const status = await gitService.status(tempDir);
      expect(status.staged.some(file => file.path === 'to-remove.txt' && file.status === 'deleted')).toBe(true);
    });
  });

  describe('mv', () => {
    beforeEach(async () => {
      await gitService.initRepo(tempDir);
    });

    it('should move/rename files in Git index', async () => {
      // Create and stage a file
      const oldFile = path.join(tempDir, 'old-name.txt');
      await fs.writeFile(oldFile, 'Content to move');
      await gitService.stage(tempDir, 'old-name.txt');
      await gitService.commit(tempDir, 'Add file to move');
      
      // Move the file
      await gitService.mv(tempDir, 'old-name.txt', 'new-name.txt');
      
      // Check status
      const status = await gitService.status(tempDir);
      expect(status.staged.some(file => file.status === 'renamed')).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should throw error for non-existent path', async () => {
      const nonExistentPath = path.join(tempDir, 'does-not-exist');
      
      await expect(gitService.status(nonExistentPath)).rejects.toThrow('does not exist or is not accessible');
    });
  });
});

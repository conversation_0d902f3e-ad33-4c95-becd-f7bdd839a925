import { ExtensionSecurityService, ExtensionPermission } from '../../../../src/main/extensions/security/extension-security.service';
import { LoggingServiceAPI } from '../../../../src/main/services/logging.service';
import { jest } from '@jest/globals';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createScopedLogger: jest.fn().mockReturnThis(),
} as unknown as LoggingServiceAPI;

describe('ExtensionSecurityService', () => {
  let securityService: ExtensionSecurityService;

  beforeEach(() => {
    jest.clearAllMocks();
    securityService = new ExtensionSecurityService(mockLogger);
  });

  afterEach(() => {
    securityService.dispose();
  });

  describe('Policy Management', () => {
    it('should create default policy for untrusted extension', () => {
      const policy = securityService.createDefaultPolicy('test-extension', false);
      
      expect(policy.extensionId).toBe('test-extension');
      expect(policy.sandboxLevel).toBe('strict');
      expect(policy.requiresUserConsent).toBe(true);
      expect(policy.permissions).toContain(ExtensionPermission.STORAGE_READ);
      expect(policy.permissions).toContain(ExtensionPermission.STORAGE_WRITE);
      expect(policy.permissions).not.toContain(ExtensionPermission.STORAGE_DELETE);
      expect(policy.permissions).not.toContain(ExtensionPermission.NETWORK_REQUEST);
    });

    it('should create default policy for trusted extension', () => {
      const policy = securityService.createDefaultPolicy('trusted-extension', true);
      
      expect(policy.extensionId).toBe('trusted-extension');
      expect(policy.sandboxLevel).toBe('moderate');
      expect(policy.requiresUserConsent).toBe(false);
      expect(policy.permissions).toContain(ExtensionPermission.STORAGE_DELETE);
      expect(policy.permissions).toContain(ExtensionPermission.NETWORK_REQUEST);
      expect(policy.permissions).toContain(ExtensionPermission.FILE_READ);
      expect(policy.permissions).toContain(ExtensionPermission.FILE_WRITE);
    });

    it('should register and retrieve extension policy', () => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
      
      const retrieved = securityService.getPolicy('test-extension');
      expect(retrieved).toEqual(policy);
    });

    it('should update extension policy', () => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
      
      securityService.updatePolicy('test-extension', {
        sandboxLevel: 'permissive',
        permissions: [...policy.permissions, ExtensionPermission.NETWORK_REQUEST]
      });
      
      const updated = securityService.getPolicy('test-extension');
      expect(updated?.sandboxLevel).toBe('permissive');
      expect(updated?.permissions).toContain(ExtensionPermission.NETWORK_REQUEST);
    });
  });

  describe('Permission Checking', () => {
    beforeEach(() => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
    });

    it('should allow permitted actions', () => {
      expect(securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_READ)).toBe(true);
      expect(securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_WRITE)).toBe(true);
      expect(securityService.hasPermission('test-extension', ExtensionPermission.IPC_HANDLE)).toBe(true);
    });

    it('should deny unpermitted actions', () => {
      expect(securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_DELETE)).toBe(false);
      expect(securityService.hasPermission('test-extension', ExtensionPermission.NETWORK_REQUEST)).toBe(false);
      expect(securityService.hasPermission('test-extension', ExtensionPermission.SYSTEM_COMMAND)).toBe(false);
    });

    it('should deny actions for unregistered extensions', () => {
      expect(securityService.hasPermission('unknown-extension', ExtensionPermission.STORAGE_READ)).toBe(false);
    });
  });

  describe('File Access Control', () => {
    beforeEach(() => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
    });

    it('should allow access to extension files', () => {
      expect(securityService.canAccessFile('test-extension', '/extensions/test-extension/file.js')).toBe(true);
      expect(securityService.canAccessFile('test-extension', '/some/path/test-extension/subfolder/file.js')).toBe(true);
    });

    it('should deny access to other extension files', () => {
      expect(securityService.canAccessFile('test-extension', '/extensions/other-extension/file.js')).toBe(false);
    });

    it('should deny access to system files', () => {
      expect(securityService.canAccessFile('test-extension', '/etc/passwd')).toBe(false);
      expect(securityService.canAccessFile('test-extension', '/path/to/node_modules/package/file.js')).toBe(false);
    });

    it('should respect custom file patterns', () => {
      securityService.updatePolicy('test-extension', {
        allowedFilePatterns: ['/custom/allowed/**'],
        deniedFilePatterns: ['/custom/denied/**']
      });

      expect(securityService.canAccessFile('test-extension', '/custom/allowed/file.js')).toBe(true);
      expect(securityService.canAccessFile('test-extension', '/custom/denied/file.js')).toBe(false);
    });
  });

  describe('Domain Access Control', () => {
    beforeEach(() => {
      const policy = securityService.createDefaultPolicy('test-extension');
      policy.trustedDomains = ['api.example.com', 'trusted.org'];
      securityService.registerExtensionPolicy(policy);
    });

    it('should allow access to trusted domains', () => {
      expect(securityService.canAccessDomain('test-extension', 'api.example.com')).toBe(true);
      expect(securityService.canAccessDomain('test-extension', 'trusted.org')).toBe(true);
      expect(securityService.canAccessDomain('test-extension', 'sub.trusted.org')).toBe(true);
    });

    it('should deny access to untrusted domains', () => {
      expect(securityService.canAccessDomain('test-extension', 'malicious.com')).toBe(false);
      expect(securityService.canAccessDomain('test-extension', 'evil.example.com')).toBe(false);
    });

    it('should allow all domains when no trusted domains specified', () => {
      const policy = securityService.createDefaultPolicy('open-extension');
      policy.trustedDomains = [];
      securityService.registerExtensionPolicy(policy);

      expect(securityService.canAccessDomain('open-extension', 'any-domain.com')).toBe(true);
    });
  });

  describe('Resource Limits', () => {
    beforeEach(() => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
    });

    it('should check resource limits', () => {
      expect(securityService.checkResourceLimits('test-extension', 'maxMemoryMB', 50)).toBe(true);
      expect(securityService.checkResourceLimits('test-extension', 'maxMemoryMB', 150)).toBe(false);
    });

    it('should update resource usage', () => {
      securityService.updateResourceUsage('test-extension', {
        memoryUsageMB: 25,
        cpuUsagePercent: 5,
        storageUsageMB: 10
      });

      const usage = securityService.getResourceUsage('test-extension');
      expect(usage?.memoryUsageMB).toBe(25);
      expect(usage?.cpuUsagePercent).toBe(5);
      expect(usage?.storageUsageMB).toBe(10);
    });

    it('should track all resource usage', () => {
      const policy2 = securityService.createDefaultPolicy('extension-2');
      securityService.registerExtensionPolicy(policy2);

      securityService.updateResourceUsage('test-extension', { memoryUsageMB: 25 });
      securityService.updateResourceUsage('extension-2', { memoryUsageMB: 35 });

      const allUsage = securityService.getAllResourceUsage();
      expect(allUsage).toHaveLength(2);
      expect(allUsage.find(u => u.extensionId === 'test-extension')?.memoryUsageMB).toBe(25);
      expect(allUsage.find(u => u.extensionId === 'extension-2')?.memoryUsageMB).toBe(35);
    });
  });

  describe('Security Violations', () => {
    beforeEach(() => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
    });

    it('should record violations when permissions are denied', () => {
      securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_DELETE);
      
      const violations = securityService.getViolations('test-extension');
      expect(violations).toHaveLength(1);
      expect(violations[0].violationType).toBe('permission');
      expect(violations[0].description).toContain('Missing permission');
    });

    it('should record violations when file access is denied', () => {
      securityService.canAccessFile('test-extension', '/etc/passwd');
      
      const violations = securityService.getViolations('test-extension');
      expect(violations).toHaveLength(1);
      expect(violations[0].violationType).toBe('sandbox');
    });

    it('should record violations when resource limits are exceeded', () => {
      securityService.checkResourceLimits('test-extension', 'maxMemoryMB', 150);
      
      const violations = securityService.getViolations('test-extension');
      expect(violations).toHaveLength(1);
      expect(violations[0].violationType).toBe('resource');
    });

    it('should clear violations for an extension', () => {
      securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_DELETE);
      expect(securityService.getViolations('test-extension')).toHaveLength(1);
      
      securityService.clearViolations('test-extension');
      expect(securityService.getViolations('test-extension')).toHaveLength(0);
    });

    it('should get all violations', () => {
      const policy2 = securityService.createDefaultPolicy('extension-2');
      securityService.registerExtensionPolicy(policy2);

      securityService.hasPermission('test-extension', ExtensionPermission.STORAGE_DELETE);
      securityService.hasPermission('extension-2', ExtensionPermission.NETWORK_REQUEST);

      const allViolations = securityService.getViolations();
      expect(allViolations).toHaveLength(2);
    });
  });

  describe('Service Disposal', () => {
    it('should dispose cleanly', () => {
      const policy = securityService.createDefaultPolicy('test-extension');
      securityService.registerExtensionPolicy(policy);
      securityService.updateResourceUsage('test-extension', { memoryUsageMB: 25 });

      securityService.dispose();

      // After disposal, service should be clean
      expect(securityService.getPolicy('test-extension')).toBeUndefined();
      expect(securityService.getResourceUsage('test-extension')).toBeUndefined();
      expect(securityService.getViolations()).toHaveLength(0);
    });
  });
});

import { ExtensionSandboxManager } from '../../../../src/main/extensions/security/extension-sandbox.manager';
import { ExtensionSecurityService } from '../../../../src/main/extensions/security/extension-security.service';
import { ExtensionManifest } from '../../../../src/main/extensions/extension.types';
import { LoggingServiceAPI } from '../../../../src/main/services/logging.service';
import { jest } from '@jest/globals';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createScopedLogger: jest.fn().mockReturnThis(),
} as unknown as LoggingServiceAPI;

// Mock manifest
const mockManifest: ExtensionManifest = {
  name: 'Test Extension',
  version: '1.0.0',
  description: 'Test extension for sandbox testing',
  main: './main.js',
  engines: {
    'book-ide': '^1.0.0'
  }
};

describe('ExtensionSandboxManager', () => {
  let sandboxManager: ExtensionSandboxManager;
  let securityService: ExtensionSecurityService;

  beforeEach(() => {
    jest.clearAllMocks();
    securityService = new ExtensionSecurityService(mockLogger);
    sandboxManager = new ExtensionSandboxManager(mockLogger, securityService);
  });

  afterEach(() => {
    sandboxManager.dispose();
    securityService.dispose();
  });

  describe('Sandbox Initialization', () => {
    it('should initialize sandbox for untrusted extension', () => {
      const config = sandboxManager.initializeSandbox('test-extension', mockManifest);
      
      expect(config.extensionId).toBe('test-extension');
      expect(config.isolateMemory).toBe(true);
      expect(config.isolateFileSystem).toBe(true);
      expect(config.isolateNetwork).toBe(true);
      expect(config.maxExecutionTime).toBe(30000); // 30s for untrusted
      expect(config.deniedModules).toContain('child_process');
      expect(config.deniedModules).toContain('fs');
      expect(config.allowedModules).toContain('path');
      expect(config.allowedModules).toContain('util');
    });

    it('should initialize sandbox for trusted extension', () => {
      // Add as trusted extension
      sandboxManager.addTrustedExtension('book-ide.trusted');
      
      const config = sandboxManager.initializeSandbox('book-ide.trusted', mockManifest);
      
      expect(config.isolateMemory).toBe(false);
      expect(config.isolateFileSystem).toBe(false);
      expect(config.isolateNetwork).toBe(false);
      expect(config.maxExecutionTime).toBe(60000); // 60s for trusted
      expect(config.deniedModules).toHaveLength(0);
      expect(config.allowedModules).toContain('child_process');
    });

    it('should recognize built-in trusted extensions', () => {
      expect(sandboxManager.isTrustedExtension('book-ide.books')).toBe(true);
      expect(sandboxManager.isTrustedExtension('book-ide.characters')).toBe(true);
      expect(sandboxManager.isTrustedExtension('unknown-extension')).toBe(false);
    });
  });

  describe('Module Access Control', () => {
    beforeEach(() => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
    });

    it('should allow safe modules', () => {
      expect(sandboxManager.canRequireModule('test-extension', 'path')).toBe(true);
      expect(sandboxManager.canRequireModule('test-extension', 'util')).toBe(true);
      expect(sandboxManager.canRequireModule('test-extension', 'events')).toBe(true);
    });

    it('should deny dangerous modules', () => {
      expect(sandboxManager.canRequireModule('test-extension', 'child_process')).toBe(false);
      expect(sandboxManager.canRequireModule('test-extension', 'fs')).toBe(false);
      expect(sandboxManager.canRequireModule('test-extension', 'http')).toBe(false);
    });

    it('should allow relative modules', () => {
      expect(sandboxManager.canRequireModule('test-extension', './local-module')).toBe(true);
      expect(sandboxManager.canRequireModule('test-extension', '../parent-module')).toBe(true);
    });

    it('should deny unauthorized third-party modules', () => {
      expect(sandboxManager.canRequireModule('test-extension', 'express')).toBe(false);
      expect(sandboxManager.canRequireModule('test-extension', 'lodash')).toBe(false);
    });
  });

  describe('Sandbox Execution', () => {
    beforeEach(() => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
    });

    it('should execute function in sandbox', async () => {
      const result = await sandboxManager.executeInSandbox('test-extension', async () => {
        return 'Hello from sandbox';
      });
      
      expect(result).toBe('Hello from sandbox');
    });

    it('should handle async functions', async () => {
      const result = await sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'Async result';
      });
      
      expect(result).toBe('Async result');
    });

    it('should enforce execution timeout', async () => {
      // Update sandbox config to have a shorter timeout for testing
      sandboxManager.updateSandboxConfig('test-extension', {
        maxExecutionTime: 100 // 100ms timeout for testing
      });

      await expect(
        sandboxManager.executeInSandbox('test-extension', async () => {
          await new Promise(resolve => setTimeout(resolve, 200)); // Longer than 100ms timeout
          return 'Should not reach here';
        })
      ).rejects.toThrow('execution timeout');
    }, 1000); // 1 second test timeout

    it('should handle execution errors', async () => {
      await expect(
        sandboxManager.executeInSandbox('test-extension', async () => {
          throw new Error('Test error');
        })
      ).rejects.toThrow('Test error');
    });

    it('should track execution context', async () => {
      const executionPromise = sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'result';
      });

      // Check that execution context exists during execution
      const context = sandboxManager.getExecutionContext('test-extension');
      expect(context).toBeDefined();
      expect(context?.isRunning).toBe(true);

      await executionPromise;

      // Context should be cleaned up after execution
      const contextAfter = sandboxManager.getExecutionContext('test-extension');
      expect(contextAfter).toBeUndefined();
    });
  });

  describe('Trusted Extension Management', () => {
    it('should add and remove trusted extensions', () => {
      expect(sandboxManager.isTrustedExtension('new-trusted')).toBe(false);
      
      sandboxManager.addTrustedExtension('new-trusted');
      expect(sandboxManager.isTrustedExtension('new-trusted')).toBe(true);
      
      sandboxManager.removeTrustedExtension('new-trusted');
      expect(sandboxManager.isTrustedExtension('new-trusted')).toBe(false);
    });
  });

  describe('Sandbox Configuration', () => {
    beforeEach(() => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
    });

    it('should get sandbox configuration', () => {
      const config = sandboxManager.getSandboxConfig('test-extension');
      expect(config).toBeDefined();
      expect(config?.extensionId).toBe('test-extension');
    });

    it('should update sandbox configuration', () => {
      sandboxManager.updateSandboxConfig('test-extension', {
        maxExecutionTime: 45000,
        allowedModules: ['path', 'util', 'crypto']
      });

      const config = sandboxManager.getSandboxConfig('test-extension');
      expect(config?.maxExecutionTime).toBe(45000);
      expect(config?.allowedModules).toContain('crypto');
    });

    it('should throw error when updating non-existent configuration', () => {
      expect(() => {
        sandboxManager.updateSandboxConfig('non-existent', { maxExecutionTime: 1000 });
      }).toThrow('No sandbox configuration found');
    });
  });

  describe('Extension Termination', () => {
    beforeEach(() => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
    });

    it('should terminate running extension', async () => {
      // Start a long-running execution
      const executionPromise = sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return 'Should be terminated';
      });

      // Terminate the extension
      sandboxManager.terminateExtension('test-extension', 'Test termination');

      // Execution should be marked as not running
      const context = sandboxManager.getExecutionContext('test-extension');
      expect(context).toBeUndefined(); // Context should be cleaned up
    });
  });

  describe('Statistics', () => {
    it('should provide sandbox statistics', () => {
      sandboxManager.initializeSandbox('extension-1', mockManifest);
      sandboxManager.initializeSandbox('extension-2', mockManifest);
      sandboxManager.addTrustedExtension('extension-1');

      const stats = sandboxManager.getSandboxStatistics();
      expect(stats.totalExtensions).toBe(2);
      expect(stats.trustedExtensions).toBe(1);
      expect(stats.untrustedExtensions).toBe(1);
      expect(stats.runningExtensions).toBe(0);
      expect(stats.violationsCount).toBe(0);
    });

    it('should track running extensions in statistics', async () => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);

      const executionPromise = sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'result';
      });

      // During execution, should show as running
      const statsDuring = sandboxManager.getSandboxStatistics();
      expect(statsDuring.runningExtensions).toBe(1);

      await executionPromise;

      // After execution, should not show as running
      const statsAfter = sandboxManager.getSandboxStatistics();
      expect(statsAfter.runningExtensions).toBe(0);
    });
  });

  describe('Execution Context Management', () => {
    beforeEach(() => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
    });

    it('should get all execution contexts', async () => {
      sandboxManager.initializeSandbox('extension-2', mockManifest);

      const execution1 = sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'result1';
      });

      const execution2 = sandboxManager.executeInSandbox('extension-2', async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return 'result2';
      });

      const contexts = sandboxManager.getAllExecutionContexts();
      expect(contexts).toHaveLength(2);
      expect(contexts.map(c => c.extensionId)).toContain('test-extension');
      expect(contexts.map(c => c.extensionId)).toContain('extension-2');

      await Promise.all([execution1, execution2]);
    });
  });

  describe('Service Disposal', () => {
    it('should dispose cleanly', () => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);
      
      const config = sandboxManager.getSandboxConfig('test-extension');
      expect(config).toBeDefined();

      sandboxManager.dispose();

      // After disposal, configurations should be cleared
      const configAfter = sandboxManager.getSandboxConfig('test-extension');
      expect(configAfter).toBeUndefined();

      const stats = sandboxManager.getSandboxStatistics();
      expect(stats.totalExtensions).toBe(0);
    });

    it('should terminate running extensions on disposal', async () => {
      sandboxManager.initializeSandbox('test-extension', mockManifest);

      // Start execution but don't wait
      const executionPromise = sandboxManager.executeInSandbox('test-extension', async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return 'result';
      });

      // Dispose should terminate running extensions
      sandboxManager.dispose();

      // Execution context should be cleaned up
      const context = sandboxManager.getExecutionContext('test-extension');
      expect(context).toBeUndefined();
    });
  });
});

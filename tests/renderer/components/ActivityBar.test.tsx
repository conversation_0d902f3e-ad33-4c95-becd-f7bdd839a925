import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import { ActivityBar } from '../../../src/renderer/components/ActivityBar/ActivityBar';
import type { ViewContainerDescriptor } from '../../../src/main/services/view.service';

// Mock the Icon component
jest.mock('../../../src/renderer/components/Icon/Icon', () => ({
  Icon: ({ name, size }: { name: string; size: number }) => (
    <span data-testid={`icon-${name}`} data-size={size}>
      {name}
    </span>
  ),
}));

describe('ActivityBar', () => {
  const mockContainers: ViewContainerDescriptor[] = [
    {
      id: 'explorer',
      title: 'Explorer',
      icon: 'FolderOpen',
      views: [],
    },
    {
      id: 'search',
      title: 'Search',
      icon: 'Search',
      views: [],
    },
    {
      id: 'source-control',
      title: 'Source Control',
      icon: 'GitBranch',
      views: [],
    },
  ];

  const mockOnContainerClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render all container buttons', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      expect(screen.getByRole('button', { name: 'Explorer' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Search' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Source Control' })).toBeInTheDocument();
    });

    it('should render icons for each container', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      expect(screen.getByTestId('icon-FolderOpen')).toBeInTheDocument();
      expect(screen.getByTestId('icon-Search')).toBeInTheDocument();
      expect(screen.getByTestId('icon-GitBranch')).toBeInTheDocument();
    });

    it('should render default icon when container has no icon', () => {
      const containersWithoutIcon: ViewContainerDescriptor[] = [
        {
          id: 'no-icon',
          title: 'No Icon',
          views: [],
        },
      ];

      render(
        <ActivityBar
          containers={containersWithoutIcon}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      expect(screen.getByTestId('icon-HelpCircle')).toBeInTheDocument();
    });

    it('should apply custom style when provided', () => {
      const customStyle = { backgroundColor: 'red', width: '100px' };

      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
          style={customStyle}
        />
      );

      const activityBar = document.querySelector('.activity-bar');
      expect(activityBar).toHaveStyle('background-color: red');
      expect(activityBar).toHaveStyle('width: 100px');
    });

    it('should render empty activity bar when no containers provided', () => {
      render(
        <ActivityBar
          containers={[]}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      const buttons = screen.queryAllByRole('button');
      expect(buttons).toHaveLength(0);
    });
  });

  describe('active state', () => {
    it('should mark active container with active class', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId="search"
          onContainerClick={mockOnContainerClick}
        />
      );

      const searchButton = screen.getByRole('button', { name: 'Search' });
      const explorerButton = screen.getByRole('button', { name: 'Explorer' });

      expect(searchButton).toHaveClass('active');
      expect(explorerButton).not.toHaveClass('active');
    });

    it('should handle null activeContainerId', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).not.toHaveClass('active');
      });
    });

    it('should handle non-existent activeContainerId', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId="non-existent"
          onContainerClick={mockOnContainerClick}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).not.toHaveClass('active');
      });
    });
  });

  describe('interactions', () => {
    it('should call onContainerClick when button is clicked', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      const searchButton = screen.getByRole('button', { name: 'Search' });
      fireEvent.click(searchButton);

      expect(mockOnContainerClick).toHaveBeenCalledWith('search');
      expect(mockOnContainerClick).toHaveBeenCalledTimes(1);
    });

    it('should call onContainerClick for each different button', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      fireEvent.click(screen.getByRole('button', { name: 'Explorer' }));
      fireEvent.click(screen.getByRole('button', { name: 'Search' }));
      fireEvent.click(screen.getByRole('button', { name: 'Source Control' }));

      expect(mockOnContainerClick).toHaveBeenCalledTimes(3);
      expect(mockOnContainerClick).toHaveBeenNthCalledWith(1, 'explorer');
      expect(mockOnContainerClick).toHaveBeenNthCalledWith(2, 'search');
      expect(mockOnContainerClick).toHaveBeenNthCalledWith(3, 'source-control');
    });

    it('should allow clicking the same button multiple times', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId="explorer"
          onContainerClick={mockOnContainerClick}
        />
      );

      const explorerButton = screen.getByRole('button', { name: 'Explorer' });
      fireEvent.click(explorerButton);
      fireEvent.click(explorerButton);

      expect(mockOnContainerClick).toHaveBeenCalledTimes(2);
      expect(mockOnContainerClick).toHaveBeenCalledWith('explorer');
    });
  });

  describe('accessibility', () => {
    it('should have proper button roles', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(3);
    });

    it('should have proper titles for accessibility', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      expect(screen.getByRole('button', { name: 'Explorer' })).toHaveAttribute('title', 'Explorer');
      expect(screen.getByRole('button', { name: 'Search' })).toHaveAttribute('title', 'Search');
      expect(screen.getByRole('button', { name: 'Source Control' })).toHaveAttribute('title', 'Source Control');
    });

    it('should be keyboard accessible', () => {
      render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId={null}
          onContainerClick={mockOnContainerClick}
        />
      );

      const searchButton = screen.getByRole('button', { name: 'Search' });
      searchButton.focus();
      fireEvent.keyDown(searchButton, { key: 'Enter' });

      expect(mockOnContainerClick).toHaveBeenCalledWith('search');
    });
  });

  describe('memoization', () => {
    it('should not re-render when props have not changed', () => {
      const { rerender } = render(
        <ActivityBar
          containers={mockContainers}
          activeContainerId="explorer"
          onContainerClick={mockOnContainerClick}
        />
      );

      const initialButton = screen.getByRole('button', { name: 'Explorer' });

      // Re-render with same props
      rerender(
        <ActivityBar
          containers={mockContainers}
          activeContainerId="explorer"
          onContainerClick={mockOnContainerClick}
        />
      );

      const afterRerenderButton = screen.getByRole('button', { name: 'Explorer' });
      expect(initialButton).toBe(afterRerenderButton);
    });
  });
});

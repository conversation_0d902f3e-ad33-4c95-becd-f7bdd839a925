import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';

// Simple test component to verify our setup works
const SimpleButton: React.FC<{ onClick: () => void; children: React.ReactNode }> = ({ 
  onClick, 
  children 
}) => (
  <button onClick={onClick}>{children}</button>
);

describe('Simple Component Test', () => {
  it('should render and handle clicks', () => {
    const mockClick = jest.fn();
    
    render(<SimpleButton onClick={mockClick}>Click me</SimpleButton>);
    
    const button = screen.getByRole('button', { name: 'Click me' });
    expect(button).toBeInTheDocument();
    
    fireEvent.click(button);
    expect(mockClick).toHaveBeenCalledTimes(1);
  });

  it('should render text content', () => {
    const mockClick = jest.fn();
    
    render(<SimpleButton onClick={mockClick}>Test Button</SimpleButton>);
    
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  it('should handle multiple clicks', () => {
    const mockClick = jest.fn();
    
    render(<SimpleButton onClick={mockClick}>Multi Click</SimpleButton>);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    fireEvent.click(button);
    fireEvent.click(button);
    
    expect(mockClick).toHaveBeenCalledTimes(3);
  });
});
